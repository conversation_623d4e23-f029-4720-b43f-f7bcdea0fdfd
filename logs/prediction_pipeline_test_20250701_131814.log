2025-07-01 13:18:14,772 - __main__ - INFO - ================================================================================
2025-07-01 13:18:14,773 - __main__ - INFO - 🧪 PREDICTION PIPELINE COMPREHENSIVE TEST
2025-07-01 13:18:14,773 - __main__ - INFO - ================================================================================
2025-07-01 13:18:14,773 - __main__ - INFO - Log file: logs/prediction_pipeline_test_20250701_131814.log
2025-07-01 13:18:14,774 - __main__ - INFO - Test started at: 2025-07-01 13:18:14.774237
2025-07-01 13:18:14,774 - __main__ - INFO - 
==================================================
2025-07-01 13:18:14,774 - __main__ - INFO - 📊 STEP 1: Testing Data Loading
2025-07-01 13:18:14,774 - __main__ - INFO - ==================================================
2025-07-01 13:18:15,175 - data_loading.utils - INFO - Found 314 available leagues
2025-07-01 13:18:15,176 - __main__ - INFO - Available leagues: 314
2025-07-01 13:18:15,176 - __main__ - INFO - ✅ League configuration loaded for ENGLAND_PREMIER_LEAGUE
2025-07-01 13:18:15,177 - __main__ - DEBUG - Config keys: ['LEAGUE_STATS_URL', 'LEAGUE_TABLE_URL', 'TEAM_URLS', 'HEAD_TO_HEAD_URLS', 'TEAM_NAME_MAPPING', 'CURRENT_LEAGUE']
2025-07-01 13:18:15,177 - __main__ - INFO - Loading data files...
2025-07-01 13:18:15,177 - data_loading.core - INFO - Loading data for league: ENGLAND_PREMIER_LEAGUE
2025-07-01 13:18:15,316 - data_loading.utils - INFO - 
Validated match results dataframe summary:
2025-07-01 13:18:15,317 - data_loading.utils - INFO - Shape: (760, 8)
2025-07-01 13:18:15,319 - data_loading.core - INFO - Loaded results dataframe with shape: (760, 8)
2025-07-01 13:18:15,365 - data_loading.utils - INFO - 
Validated team statistics dataframe summary:
2025-07-01 13:18:15,365 - data_loading.utils - INFO - Shape: (20, 62)
2025-07-01 13:18:15,372 - data_loading.core - INFO - Loaded team_stats dataframe with shape: (20, 62)
2025-07-01 13:18:15,379 - data_loading.utils - INFO - 
Validated league statistics dataframe summary:
2025-07-01 13:18:15,379 - data_loading.utils - INFO - Shape: (30, 2)
2025-07-01 13:18:15,380 - data_loading.core - INFO - Loaded league_stats dataframe with shape: (30, 2)
2025-07-01 13:18:15,403 - data_loading.utils - INFO - 
Validated head-to-head statistics dataframe summary:
2025-07-01 13:18:15,404 - data_loading.utils - INFO - Shape: (184, 19)
2025-07-01 13:18:15,407 - data_loading.utils - WARNING - Missing values in head-to-head statistics:
2025-07-01 13:18:15,408 - data_loading.utils - WARNING -   home_team_name: 184 missing values
2025-07-01 13:18:15,408 - data_loading.utils - WARNING -   away_team_name: 184 missing values
2025-07-01 13:18:15,409 - data_loading.core - INFO - Loaded h2h_stats dataframe with shape: (184, 19)
2025-07-01 13:18:15,418 - data_loading.utils - INFO - 
Validated league table dataframe summary:
2025-07-01 13:18:15,418 - data_loading.utils - INFO - Shape: (20, 10)
2025-07-01 13:18:15,421 - data_loading.core - INFO - Loaded league_table dataframe with shape: (20, 10)
2025-07-01 13:18:15,421 - data_loading.core - INFO - Successfully loaded all data files for league: ENGLAND_PREMIER_LEAGUE
2025-07-01 13:18:15,421 - __main__ - INFO - ✅ Data loaded successfully:
2025-07-01 13:18:15,421 - __main__ - INFO -    - Results: 760 matches
2025-07-01 13:18:15,421 - __main__ - INFO -    - Team stats: 20 teams
2025-07-01 13:18:15,422 - __main__ - INFO -    - League stats: 30 statistics
2025-07-01 13:18:15,422 - __main__ - INFO -    - H2H stats: 184 matchups
2025-07-01 13:18:15,422 - __main__ - INFO -    - League table: 20 teams
2025-07-01 13:18:15,422 - __main__ - INFO - 
==================================================
2025-07-01 13:18:15,422 - __main__ - INFO - 🔧 STEP 2: Testing Feature Preparation
2025-07-01 13:18:15,422 - __main__ - INFO - ==================================================
2025-07-01 13:18:17,531 - __main__ - INFO - Preparing features...
2025-07-01 13:18:17,532 - feature_engineering.core - INFO - Starting feature preparation for 760 matches
2025-07-01 13:18:27,514 - feature_engineering.core - INFO - Processing 760 valid matches. Skipped 0 matches.
2025-07-01 13:18:27,515 - feature_engineering.core - INFO - Unique labels for three_way: ['Home' 'Away' 'Draw']
2025-07-01 13:18:27,519 - feature_engineering.core - INFO - Unique labels for over_under_1_5: ['Over 1.5' 'Under 1.5']
2025-07-01 13:18:27,521 - feature_engineering.core - INFO - Unique labels for over_under_2_5: ['Under 2.5' 'Over 2.5']
2025-07-01 13:18:27,525 - feature_engineering.core - INFO - Unique labels for over_under_3_5: ['Under 3.5' 'Over 3.5']
2025-07-01 13:18:27,529 - feature_engineering.core - INFO - Unique labels for btts: ['No' 'Yes']
2025-07-01 13:18:27,532 - __main__ - INFO - ✅ Features prepared successfully:
2025-07-01 13:18:27,532 - __main__ - INFO -    - Shape: (760, 46)
2025-07-01 13:18:27,532 - __main__ - INFO -    - Columns: 46
2025-07-01 13:18:27,533 - __main__ - DEBUG -    - Column names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength', 'home_form', 'away_form', 'home_recent_scoring_rate', 'away_recent_scoring_rate', 'home_recent_conceding_rate', 'away_recent_conceding_rate', 'home_team_position', 'away_team_position', 'position_difference', 'h2h_home_recent_win_rate', 'h2h_away_recent_win_rate', 'h2h_recent_draw_rate', 'h2h_team_a_win_percentage', 'h2h_team_b_win_percentage', 'h2h_draw_percentage', 'h2h_total_matches', 'h2h_team_a_goals', 'h2h_team_b_goals', 'h2h_btts_percentage', 'h2h_goal_pattern', 'Team', 'Home Team', 'Away Team', 'form_data_valid_str', 'form_data_valid', 'result', 'three_way', 'btts', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'three_way_encoded', 'over_under_1_5_encoded', 'over_under_2_5_encoded', 'over_under_3_5_encoded', 'btts_encoded']
2025-07-01 13:18:27,537 - __main__ - INFO - ✅ No missing values found
2025-07-01 13:18:27,539 - __main__ - INFO - ✅ X data shape: (760, 34)
2025-07-01 13:18:27,539 - __main__ - INFO - ✅ Y data targets: ['three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts']
2025-07-01 13:18:27,540 - __main__ - INFO - ✅ Label encoders: ['three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts']
2025-07-01 13:18:27,540 - __main__ - INFO - 
==================================================
2025-07-01 13:18:27,540 - __main__ - INFO - 🤖 STEP 3: Testing Model Training
2025-07-01 13:18:27,540 - __main__ - INFO - ==================================================
2025-07-01 13:18:34,255 - h5py._conv - DEBUG - Creating converter from 7 to 5
2025-07-01 13:18:34,256 - h5py._conv - DEBUG - Creating converter from 5 to 7
2025-07-01 13:18:34,256 - h5py._conv - DEBUG - Creating converter from 7 to 5
2025-07-01 13:18:34,256 - h5py._conv - DEBUG - Creating converter from 5 to 7
2025-07-01 13:18:39,045 - __main__ - INFO - Training models...
2025-07-01 13:18:39,046 - model_training.core - INFO - Starting train_model function
2025-07-01 13:18:39,046 - model_training.core - INFO - Shape of X: (760, 34)
2025-07-01 13:18:39,067 - model_training.core - INFO - 
Training model for three_way
2025-07-01 13:18:40,235 - model_training.core - INFO - Class weights after adjustment: {0: np.float64(2.0132231044886666), 1: np.float64(3.249379200014064), 2: np.float64(1.3464825772518079)}
2025-07-01 13:19:03,251 - model_training.core - INFO - Three_way Model Accuracy: 0.5000
2025-07-01 13:19:03,252 - model_training.core - INFO - 
Classification Report:
2025-07-01 13:19:03,284 - model_training.core - INFO -               precision    recall  f1-score   support

        Away       0.58      0.55      0.56        62
        Draw       0.22      0.44      0.30        27
        Home       0.77      0.48      0.59        63

    accuracy                           0.50       152
   macro avg       0.52      0.49      0.48       152
weighted avg       0.59      0.50      0.53       152

2025-07-01 13:19:03,900 - model_training.feature_analysis - ERROR - Error in neural network feature importance analysis: analyze_neural_network_importance.<locals>.scoring_fn() takes 2 positional arguments but 3 were given
2025-07-01 13:19:05,195 - model_training.core - INFO - 
Training model for over_under_1_5
2025-07-01 13:19:05,982 - model_training.core - ERROR - Error calculating prediction weights: index 0 is out of bounds for axis 0 with size 0
2025-07-01 13:19:15,820 - model_training.core - INFO - Over_under_1_5 Model Accuracy: 0.8289
2025-07-01 13:19:15,820 - model_training.core - INFO - 
Classification Report:
2025-07-01 13:19:15,843 - model_training.core - INFO -               precision    recall  f1-score   support

    Over 1.5       0.86      0.94      0.90       126
   Under 1.5       0.50      0.27      0.35        26

    accuracy                           0.83       152
   macro avg       0.68      0.61      0.63       152
weighted avg       0.80      0.83      0.81       152

2025-07-01 13:19:16,735 - model_training.feature_analysis - ERROR - Error in feature importance analysis: 'CalibratedClassifierCV' object has no attribute 'base_estimator'
2025-07-01 13:19:16,735 - model_training.core - INFO - 
Training model for over_under_2_5
2025-07-01 13:19:17,309 - model_training.core - ERROR - Error calculating prediction weights: index 0 is out of bounds for axis 0 with size 0
2025-07-01 13:19:26,565 - model_training.core - INFO - Over_under_2_5 Model Accuracy: 0.6842
2025-07-01 13:19:26,565 - model_training.core - INFO - 
Classification Report:
2025-07-01 13:19:26,587 - model_training.core - INFO -               precision    recall  f1-score   support

    Over 2.5       0.69      0.85      0.76        91
   Under 2.5       0.66      0.44      0.53        61

    accuracy                           0.68       152
   macro avg       0.68      0.64      0.65       152
weighted avg       0.68      0.68      0.67       152

2025-07-01 13:19:27,089 - model_training.feature_analysis - ERROR - Error in feature importance analysis: 'CalibratedClassifierCV' object has no attribute 'base_estimator'
2025-07-01 13:19:27,090 - model_training.core - INFO - 
Training model for over_under_3_5
2025-07-01 13:19:27,689 - model_training.core - ERROR - Error calculating prediction weights: index 0 is out of bounds for axis 0 with size 0
2025-07-01 13:19:36,554 - model_training.core - INFO - Over_under_3_5 Model Accuracy: 0.6908
2025-07-01 13:19:36,555 - model_training.core - INFO - 
Classification Report:
2025-07-01 13:19:36,573 - model_training.core - INFO -               precision    recall  f1-score   support

    Over 3.5       0.59      0.54      0.56        56
   Under 3.5       0.74      0.78      0.76        96

    accuracy                           0.69       152
   macro avg       0.67      0.66      0.66       152
weighted avg       0.69      0.69      0.69       152

2025-07-01 13:19:36,964 - model_training.feature_analysis - ERROR - Error in feature importance analysis: 'CalibratedClassifierCV' object has no attribute 'base_estimator'
2025-07-01 13:19:36,965 - model_training.core - INFO - 
Training model for btts
2025-07-01 13:19:46,554 - model_training.core - INFO - Btts Model Accuracy: 0.5855
2025-07-01 13:19:46,555 - model_training.core - INFO - 
Classification Report:
2025-07-01 13:19:46,582 - model_training.core - INFO -               precision    recall  f1-score   support

          No       0.49      0.57      0.53        61
         Yes       0.68      0.59      0.63        91

    accuracy                           0.59       152
   macro avg       0.58      0.58      0.58       152
weighted avg       0.60      0.59      0.59       152

2025-07-01 13:19:47,076 - model_training.feature_analysis - ERROR - Error in feature importance analysis: 'CalibratedClassifierCV' object has no attribute 'base_estimator'
2025-07-01 13:19:47,077 - __main__ - INFO - ✅ Models trained successfully:
2025-07-01 13:19:47,078 - __main__ - INFO -    - three_way: Sequential
2025-07-01 13:19:47,079 - __main__ - INFO -      Features: 31
2025-07-01 13:19:47,079 - __main__ - DEBUG -      Feature names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength']...
2025-07-01 13:19:47,079 - __main__ - INFO -    - over_under_1_5: CalibratedClassifierCV
2025-07-01 13:19:47,079 - __main__ - INFO -      Features: 31
2025-07-01 13:19:47,080 - __main__ - DEBUG -      Feature names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength']...
2025-07-01 13:19:47,080 - __main__ - INFO -    - over_under_2_5: CalibratedClassifierCV
2025-07-01 13:19:47,080 - __main__ - INFO -      Features: 31
2025-07-01 13:19:47,080 - __main__ - DEBUG -      Feature names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength']...
2025-07-01 13:19:47,080 - __main__ - INFO -    - over_under_3_5: CalibratedClassifierCV
2025-07-01 13:19:47,080 - __main__ - INFO -      Features: 31
2025-07-01 13:19:47,081 - __main__ - DEBUG -      Feature names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength']...
2025-07-01 13:19:47,081 - __main__ - INFO -    - btts: CalibratedClassifierCV
2025-07-01 13:19:47,081 - __main__ - INFO -      Features: 31
2025-07-01 13:19:47,082 - __main__ - DEBUG -      Feature names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength']...
2025-07-01 13:19:47,083 - __main__ - INFO - 
==================================================
2025-07-01 13:19:47,083 - __main__ - INFO - ⚽ STEP 4: Testing Single Match Prediction
2025-07-01 13:19:47,083 - __main__ - INFO - ==================================================
2025-07-01 13:19:47,440 - __main__ - DEBUG - Available teams: ['Arsenal', 'Aston Villa', 'Bournemouth', 'Brentford', 'Brighton', 'Chelsea', 'Crystal Palace', 'Everton', 'Fulham', 'Ipswich Town', 'Leicester City', 'Liverpool', 'Manchester City', 'Manchester Utd', 'Newcastle Utd', 'Nottm Forest', 'Southampton', 'Tottenham', 'West Ham Utd', 'Wolverhampton']
2025-07-01 13:19:47,441 - __main__ - INFO - Testing prediction for: Liverpool vs Southampton
2025-07-01 13:19:47,448 - __main__ - INFO - 📊 Liverpool stats: PPG=2.21, Goals/Game=2.26
2025-07-01 13:19:47,449 - __main__ - INFO - 📊 Southampton stats: PPG=0.32, Goals/Game=0.68
2025-07-01 13:19:47,449 - prediction.core - DEBUG - Starting predict_match for Liverpool vs Southampton
2025-07-01 13:19:47,461 - prediction.expected_goals - DEBUG - Starting expected goals calculation
2025-07-01 13:19:47,463 - prediction.expected_goals - DEBUG - Scoring rates - Home: 0.88, Away: 0.27
2025-07-01 13:19:47,463 - prediction.expected_goals - DEBUG - Conceding rates - Home: 0.34, Away: 0.82
2025-07-01 13:19:47,464 - prediction.expected_goals - DEBUG - League averages - Home: 1.51, Away: 1.42
2025-07-01 13:19:47,464 - prediction.expected_goals - DEBUG - Attack strengths - Home: 1.73, Away: 0.80
2025-07-01 13:19:47,466 - prediction.expected_goals - DEBUG - Defense strengths - Home: 5.95, Away: 2.44
2025-07-01 13:19:47,467 - prediction.expected_goals - DEBUG - Form factors - Home: 0.74, Away: 0.11
2025-07-01 13:19:47,468 - prediction.expected_goals - DEBUG - Base xG - Home: 1.35, Away: 0.61
2025-07-01 13:19:47,468 - prediction.expected_goals - INFO - Team strengths - Home: 0.81, Away: 0.11
2025-07-01 13:19:47,468 - prediction.expected_goals - DEBUG - After home advantage adjustment - Home xG: 1.62, Away xG: 0.51
2025-07-01 13:19:47,468 - prediction.expected_goals - INFO - Final expected goals - Home: 1.62, Away: 0.51
2025-07-01 13:19:47,468 - prediction.core - INFO - Expected goals - Home: 1.62, Away: 0.51
2025-07-01 13:19:47,472 - prediction.core - INFO - 
Feature values for Liverpool vs Southampton:
2025-07-01 13:19:47,473 - prediction.core - INFO - home_points_per_game: 0.736842105263158
2025-07-01 13:19:47,473 - prediction.core - INFO - away_points_per_game: 0.10526315789473684
2025-07-01 13:19:47,474 - prediction.core - INFO - home_goals_scored_ratio: 0.4878587196467991
2025-07-01 13:19:47,474 - prediction.core - INFO - away_goals_scored_ratio: 0.15962441314553993
2025-07-01 13:19:47,474 - prediction.core - INFO - home_goals_conceded_ratio: 0.18543046357615892
2025-07-01 13:19:47,474 - prediction.core - INFO - away_goals_conceded_ratio: 0.48122065727699526
2025-07-01 13:19:47,474 - prediction.core - INFO - home_attack_strength: 1.7298542955855727
2025-07-01 13:19:47,474 - prediction.core - INFO - away_attack_strength: 0.803623217967759
2025-07-01 13:19:47,474 - prediction.core - INFO - home_defense_strength: 1.8116294285600556
2025-07-01 13:19:47,475 - prediction.core - INFO - away_defense_strength: 1.3286770533538377
2025-07-01 13:19:47,475 - prediction.core - INFO - home_form: 0.8149319842672149
2025-07-01 13:19:47,475 - prediction.core - INFO - away_form: 0.09140191503252103
2025-07-01 13:19:47,476 - prediction.core - INFO - home_recent_scoring_rate: 2.26
2025-07-01 13:19:47,477 - prediction.core - INFO - away_recent_scoring_rate: 0.68
2025-07-01 13:19:47,478 - prediction.core - INFO - home_recent_conceding_rate: 1.08
2025-07-01 13:19:47,478 - prediction.core - INFO - away_recent_conceding_rate: 2.26
2025-07-01 13:19:47,478 - prediction.core - INFO - home_team_position: 0.5
2025-07-01 13:19:47,478 - prediction.core - INFO - away_team_position: 0.5
2025-07-01 13:19:47,478 - prediction.core - INFO - position_difference: 0
2025-07-01 13:19:47,478 - prediction.core - INFO - h2h_home_recent_win_rate: 0.41
2025-07-01 13:19:47,478 - prediction.core - INFO - h2h_away_recent_win_rate: 0.35
2025-07-01 13:19:47,479 - prediction.core - INFO - h2h_recent_draw_rate: 0.24
2025-07-01 13:19:47,479 - prediction.core - INFO - h2h_team_a_win_percentage: 0.41
2025-07-01 13:19:47,479 - prediction.core - INFO - h2h_team_b_win_percentage: 0.35
2025-07-01 13:19:47,479 - prediction.core - INFO - h2h_draw_percentage: 0.24
2025-07-01 13:19:47,479 - prediction.core - INFO - h2h_total_matches: 0
2025-07-01 13:19:47,480 - prediction.core - INFO - h2h_team_a_goals: 1.51
2025-07-01 13:19:47,481 - prediction.core - INFO - h2h_team_b_goals: 1.42
2025-07-01 13:19:47,481 - prediction.core - INFO - h2h_btts_percentage: 0.57
2025-07-01 13:19:47,481 - prediction.core - INFO - h2h_goal_pattern: 0
2025-07-01 13:19:47,481 - prediction.core - INFO - Team: Liverpool
2025-07-01 13:19:47,482 - prediction.core - INFO - Home Team: Liverpool
2025-07-01 13:19:47,482 - prediction.core - INFO - Away Team: Southampton
2025-07-01 13:19:47,482 - prediction.core - INFO - expected_goals_home: 1.620477535301669
2025-07-01 13:19:47,482 - prediction.core - INFO - expected_goals_away: 0.5142606516290725
2025-07-01 13:19:47,482 - prediction.core - INFO - expected_goals_total: 2.1347381869307416
2025-07-01 13:19:47,482 - prediction.core - INFO - form_data_valid: 1
2025-07-01 13:19:47,488 - prediction.core - DEBUG - Scaling features for three_way prediction
2025-07-01 13:19:47,734 - prediction.core - DEBUG - Scaling features for over_under_1_5 prediction
2025-07-01 13:19:48,062 - prediction.core - DEBUG - Scaling features for over_under_2_5 prediction
2025-07-01 13:19:48,398 - prediction.core - DEBUG - Scaling features for over_under_3_5 prediction
2025-07-01 13:19:48,716 - prediction.core - DEBUG - Scaling features for btts prediction
2025-07-01 13:19:49,084 - prediction.probabilities - DEBUG - Calculating double chance probabilities from: {'Away': np.float64(0.21949210611554731), 'Draw': np.float64(0.3052078485488891), 'Home': np.float64(0.4753000453355635)}
2025-07-01 13:19:49,085 - prediction.probabilities - DEBUG - Double chance probabilities: {'Home or Draw': np.float64(0.38080883780703945), 'Away or Draw': np.float64(0.2664138399108938), 'Home or Away': np.float64(0.35277732228206676)}
2025-07-01 13:19:49,086 - prediction.scores - DEBUG - Starting score prediction with expected goals: (np.float64(1.620477535301669), np.float64(0.5142606516290725))
2025-07-01 13:19:49,086 - prediction.scores - DEBUG - Starting Dixon-Coles probability calculation
2025-07-01 13:19:49,087 - prediction.scores - DEBUG - Dixon-Coles expected goals - Home: 1.98, Away: 0.47
2025-07-01 13:19:49,146 - prediction.scores - DEBUG - Dixon-Coles probability calculation completed
2025-07-01 13:19:49,211 - prediction.scores - INFO - Score prediction completed
2025-07-01 13:19:49,212 - __main__ - INFO - ✅ Prediction completed successfully!
2025-07-01 13:19:49,212 - __main__ - INFO - 📊 PREDICTION RESULTS:
2025-07-01 13:19:49,212 - __main__ - INFO - Match: Liverpool vs Southampton
2025-07-01 13:19:49,212 - __main__ - INFO - 
THREE_WAY:
2025-07-01 13:19:49,212 - __main__ - INFO -    Prediction: Home
2025-07-01 13:19:49,213 - __main__ - INFO -    Probabilities:
2025-07-01 13:19:49,213 - __main__ - INFO -      Away: 0.219
2025-07-01 13:19:49,213 - __main__ - INFO -      Draw: 0.305
2025-07-01 13:19:49,214 - __main__ - INFO -      Home: 0.475
2025-07-01 13:19:49,216 - __main__ - INFO - 
OVER_UNDER_1_5:
2025-07-01 13:19:49,217 - __main__ - INFO -    Prediction: Over 1.5
2025-07-01 13:19:49,218 - __main__ - INFO -    Probabilities:
2025-07-01 13:19:49,219 - __main__ - INFO -      Under 1.5: 0.383
2025-07-01 13:19:49,220 - __main__ - INFO -      Over 1.5: 0.617
2025-07-01 13:19:49,220 - __main__ - INFO - 
OVER_UNDER_2_5:
2025-07-01 13:19:49,221 - __main__ - INFO -    Prediction: Over 2.5
2025-07-01 13:19:49,222 - __main__ - INFO -    Probabilities:
2025-07-01 13:19:49,223 - __main__ - INFO -      Under 2.5: 0.482
2025-07-01 13:19:49,223 - __main__ - INFO -      Over 2.5: 0.518
2025-07-01 13:19:49,223 - __main__ - INFO - 
OVER_UNDER_3_5:
2025-07-01 13:19:49,224 - __main__ - INFO -    Prediction: Under 3.5
2025-07-01 13:19:49,224 - __main__ - INFO -    Probabilities:
2025-07-01 13:19:49,224 - __main__ - INFO -      Under 3.5: 0.665
2025-07-01 13:19:49,224 - __main__ - INFO -      Over 3.5: 0.335
2025-07-01 13:19:49,224 - __main__ - INFO - 
BTTS:
2025-07-01 13:19:49,225 - __main__ - INFO -    Prediction: Yes
2025-07-01 13:19:49,225 - __main__ - INFO -    Probabilities:
2025-07-01 13:19:49,225 - __main__ - INFO -      No: 0.429
2025-07-01 13:19:49,225 - __main__ - INFO -      Yes: 0.571
2025-07-01 13:19:49,225 - __main__ - INFO - 
DOUBLE_CHANCE:
2025-07-01 13:19:49,226 - __main__ - INFO -    Prediction: Home or Draw
2025-07-01 13:19:49,226 - __main__ - INFO -    Probabilities:
2025-07-01 13:19:49,226 - __main__ - INFO -      Home or Draw: 0.381
2025-07-01 13:19:49,226 - __main__ - INFO -      Away or Draw: 0.266
2025-07-01 13:19:49,227 - __main__ - INFO -      Home or Away: 0.353
2025-07-01 13:19:49,227 - __main__ - INFO - 
Expected Goals:
2025-07-01 13:19:49,227 - __main__ - INFO -    Home: 1.62
2025-07-01 13:19:49,227 - __main__ - INFO -    Away: 0.51
2025-07-01 13:19:49,227 - __main__ - INFO - 
Top Correct Scores:
2025-07-01 13:19:49,228 - __main__ - INFO -    1-0: 0.195
2025-07-01 13:19:49,228 - __main__ - INFO -    2-0: 0.183
2025-07-01 13:19:49,228 - __main__ - INFO -    3-0: 0.110
2025-07-01 13:19:49,229 - __main__ - INFO -    2-1: 0.088
2025-07-01 13:19:49,229 - __main__ - INFO -    0-0: 0.087
2025-07-01 13:19:49,229 - __main__ - INFO - 
==================================================
2025-07-01 13:19:49,229 - __main__ - INFO - 📈 STEP 5: Testing Analysis and Output
2025-07-01 13:19:49,230 - __main__ - INFO - ==================================================
2025-07-01 13:19:49,230 - __main__ - INFO - Analyzing prediction confidence...
2025-07-01 13:19:49,230 - prediction.analysis - DEBUG - Starting prediction confidence analysis
2025-07-01 13:19:49,230 - prediction.analysis - DEBUG - Maximum probability for three_way: 0.475
2025-07-01 13:19:49,231 - prediction.analysis - DEBUG - Maximum probability for over_under_1_5: 0.617
2025-07-01 13:19:49,231 - prediction.analysis - DEBUG - Maximum probability for over_under_2_5: 0.518
2025-07-01 13:19:49,231 - prediction.analysis - DEBUG - Maximum probability for over_under_3_5: 0.665
2025-07-01 13:19:49,231 - prediction.analysis - DEBUG - Maximum probability for btts: 0.571
2025-07-01 13:19:49,232 - prediction.analysis - DEBUG - Maximum probability for double_chance: 0.381
2025-07-01 13:19:49,232 - __main__ - INFO - ✅ Confidence analysis completed
2025-07-01 13:19:49,232 - __main__ - DEBUG - Confidence analysis: {'three_way': {'confidence_level': 'Low', 'max_probability': np.float64(0.4753000453355635), 'probability_difference': np.float64(0.17009219678667442), 'prediction_strength': 'Weak'}, 'over_under_1_5': {'confidence_level': 'Medium', 'max_probability': np.float64(0.6168509427088872), 'probability_difference': np.float64(0.2337018854177743), 'prediction_strength': 'Weak'}, 'over_under_2_5': {'confidence_level': 'Low', 'max_probability': np.float64(0.518145991644217), 'probability_difference': np.float64(0.03629198328843397), 'prediction_strength': 'Weak'}, 'over_under_3_5': {'confidence_level': 'Medium', 'max_probability': np.float64(0.6649468871782378), 'probability_difference': np.float64(0.3298937743564756), 'prediction_strength': 'Weak'}, 'btts': {'confidence_level': 'Medium', 'max_probability': 0.5707283470863255, 'probability_difference': 0.14145669417265094, 'prediction_strength': 'Weak'}, 'double_chance': {'confidence_level': 'Low', 'max_probability': np.float64(0.38080883780703945), 'probability_difference': np.float64(0.11439499789614566), 'prediction_strength': 'Weak'}}
2025-07-01 13:19:49,232 - __main__ - INFO - Assessing prediction risk...
2025-07-01 13:19:49,232 - prediction.analysis - DEBUG - Starting prediction risk assessment
2025-07-01 13:19:49,233 - __main__ - INFO - ✅ Risk assessment completed
2025-07-01 13:19:49,233 - __main__ - DEBUG - Risk assessment: {'three_way': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.4753000453355635), 'probability_difference': np.float64(0.17009219678667442), 'prediction_balance': 'Clear Favorite'}}, 'over_under_1_5': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': np.float64(0.6168509427088872), 'probability_distribution': 'Skewed'}}, 'over_under_2_5': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.518145991644217), 'probability_distribution': 'Balanced'}}, 'over_under_3_5': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': np.float64(0.6649468871782378), 'probability_distribution': 'Skewed'}}, 'btts': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': 0.5707283470863255, 'probability_distribution': 'Balanced'}}, 'double_chance': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.38080883780703945), 'probability_difference': np.float64(0.11439499789614566)}}, 'overall': {'risk_level': 'High', 'risk_score': 2.75}}
2025-07-01 13:19:49,233 - __main__ - INFO - Generating prediction summary...
2025-07-01 13:19:49,234 - prediction.analysis - DEBUG - Generating prediction summary
2025-07-01 13:19:49,234 - prediction.analysis - DEBUG - Prediction summary generated successfully
2025-07-01 13:19:49,235 - __main__ - INFO - ✅ Prediction summary generated
2025-07-01 13:19:49,235 - __main__ - INFO - Saving predictions to Excel...
2025-07-01 13:19:49,235 - prediction.excel_output - INFO - Starting Excel output process
2025-07-01 13:19:49,235 - prediction.excel_output - INFO - Processing predictions for ENGLAND_PREMIER_LEAGUE_TEST:
2025-07-01 13:19:49,235 - prediction.excel_output - INFO - 
Match: Test Match
2025-07-01 13:19:49,236 - prediction.excel_output - INFO - Prediction data: {'main_predictions': {'three_way': {'prediction': 'Home', 'probabilities': {'Away': np.float64(0.21949210611554731), 'Draw': np.float64(0.3052078485488891), 'Home': np.float64(0.4753000453355635)}}, 'over_under_1_5': {'prediction': 'Over 1.5', 'probabilities': {'Under 1.5': np.float64(0.38314905729111287), 'Over 1.5': np.float64(0.6168509427088872)}}, 'over_under_2_5': {'prediction': 'Over 2.5', 'probabilities': {'Under 2.5': np.float64(0.48185400835578307), 'Over 2.5': np.float64(0.518145991644217)}}, 'over_under_3_5': {'prediction': 'Under 3.5', 'probabilities': {'Under 3.5': np.float64(0.6649468871782378), 'Over 3.5': np.float64(0.3350531128217622)}}, 'btts': {'prediction': 'Yes', 'probabilities': {'No': 0.42927165291367453, 'Yes': 0.5707283470863255}}, 'double_chance': {'prediction': 'Home or Draw', 'probabilities': {'Home or Draw': np.float64(0.38080883780703945), 'Away or Draw': np.float64(0.2664138399108938), 'Home or Away': np.float64(0.35277732228206676)}}}, 'confidence_levels': {'three_way': {'confidence_level': 'Low', 'max_probability': np.float64(0.4753000453355635), 'probability_difference': np.float64(0.17009219678667442), 'prediction_strength': 'Weak'}, 'over_under_1_5': {'confidence_level': 'Medium', 'max_probability': np.float64(0.6168509427088872), 'probability_difference': np.float64(0.2337018854177743), 'prediction_strength': 'Weak'}, 'over_under_2_5': {'confidence_level': 'Low', 'max_probability': np.float64(0.518145991644217), 'probability_difference': np.float64(0.03629198328843397), 'prediction_strength': 'Weak'}, 'over_under_3_5': {'confidence_level': 'Medium', 'max_probability': np.float64(0.6649468871782378), 'probability_difference': np.float64(0.3298937743564756), 'prediction_strength': 'Weak'}, 'btts': {'confidence_level': 'Medium', 'max_probability': 0.5707283470863255, 'probability_difference': 0.14145669417265094, 'prediction_strength': 'Weak'}, 'double_chance': {'confidence_level': 'Low', 'max_probability': np.float64(0.38080883780703945), 'probability_difference': np.float64(0.11439499789614566), 'prediction_strength': 'Weak'}}, 'score_predictions': {'most_likely_scores': {'1-0': np.float64(0.19454142462908364), '2-0': np.float64(0.18285359360556752), '3-0': np.float64(0.11008114243253564), '2-1': np.float64(0.08826711235289651), '0-0': np.float64(0.08727586904686088)}, 'expected_total_goals': np.float64(2.3681270960153284)}, 'value_analysis': {}, 'risk_assessment': {'three_way': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.4753000453355635), 'probability_difference': np.float64(0.17009219678667442), 'prediction_balance': 'Clear Favorite'}}, 'over_under_1_5': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': np.float64(0.6168509427088872), 'probability_distribution': 'Skewed'}}, 'over_under_2_5': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.518145991644217), 'probability_distribution': 'Balanced'}}, 'over_under_3_5': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': np.float64(0.6649468871782378), 'probability_distribution': 'Skewed'}}, 'btts': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': 0.5707283470863255, 'probability_distribution': 'Balanced'}}, 'double_chance': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.38080883780703945), 'probability_difference': np.float64(0.11439499789614566)}}, 'overall': {'risk_level': 'High', 'risk_score': 2.75}}}
2025-07-01 13:19:49,243 - prediction.excel_output - ERROR - Error processing match Test Match: not enough values to unpack (expected 2, got 1)
2025-07-01 13:19:49,303 - prediction.excel_output - INFO - Predictions saved to /home/<USER>/projects/betting_project/data/processed/ENGLAND_PREMIER_LEAGUE_TEST_match_predictions.xlsx
2025-07-01 13:19:49,305 - __main__ - INFO - ✅ Predictions saved to Excel file
2025-07-01 13:19:49,306 - __main__ - INFO - 
================================================================================
2025-07-01 13:19:49,308 - __main__ - INFO - 🎉 PREDICTION PIPELINE TEST COMPLETED SUCCESSFULLY!
2025-07-01 13:19:49,309 - __main__ - INFO - ================================================================================
2025-07-01 13:19:49,310 - __main__ - INFO - ✅ All pipeline components tested successfully:
2025-07-01 13:19:49,312 - __main__ - INFO -    ✓ Data loading
2025-07-01 13:19:49,313 - __main__ - INFO -    ✓ Feature preparation
2025-07-01 13:19:49,314 - __main__ - INFO -    ✓ Model training
2025-07-01 13:19:49,315 - __main__ - INFO -    ✓ Match prediction
2025-07-01 13:19:49,315 - __main__ - INFO -    ✓ Analysis and output
2025-07-01 13:19:49,315 - __main__ - INFO - 
📄 Detailed logs saved to: logs/prediction_pipeline_test_20250701_131814.log
2025-07-01 13:19:49,316 - __main__ - INFO - 📊 Excel output saved to: data/processed/
2025-07-01 13:19:49,317 - __main__ - INFO - 🕒 Test completed at: 2025-07-01 13:19:49.317229
