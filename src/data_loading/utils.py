"""
Utility functions for data loading operations.
"""

import logging
import pandas as pd
from dateutil.parser import parse
from typing import List, Optional
import os
from .constants import RAW_DATA_DIR

logger = logging.getLogger(__name__)

def parse_date(date_str: str) -> pd.Timestamp:
    """
    Parse date string to timestamp with error handling.

    Args:
        date_str: Date string to parse

    Returns:
        Parsed timestamp or NaT if parsing fails

    Example:
        >>> ts = parse_date("2023-01-01")
        >>> print(ts.strftime("%Y-%m-%d"))
        2023-01-01
    """
    try:
        return parse(date_str, fuzzy=True)
    except (ValueError, TypeError) as e:
        logger.warning(f"Could not parse date '{date_str}': {str(e)}")
        return pd.NaT

def get_available_leagues() -> List[str]:
    """
    Get list of available leagues from the raw data directory.

    Returns:
        List of league names that have data available

    Example:
        >>> leagues = get_available_leagues()
        >>> print(leagues)
        ['ENGLAND_PREMIER_LEAGUE', 'SPAIN_LA_LIGA']
    """
    try:
        leagues = [
            d
            for d in os.listdir(RAW_DATA_DIR)
            if os.path.isdir(os.path.join(RAW_DATA_DIR, d))
        ]
        logger.info(f"Found {len(leagues)} available leagues")
        return leagues
    except Exception as e:
        logger.error(f"Error getting available leagues: {str(e)}")
        return []

def get_file_path(league_name: str, data_type: str) -> str:
    """
    Generate file path for a specific league and data type.

    Args:
        league_name: Name of the league
        data_type: Type of data file ('results', 'team_stats', etc.)

    Returns:
        Full path to the data file
    """
    from .constants import DATA_FILES
    filename = DATA_FILES[data_type].format(league_name=league_name)
    return os.path.join(RAW_DATA_DIR, league_name, filename)

def log_dataframe_info(df: pd.DataFrame, data_type: str) -> None:
    """
    Log information about a DataFrame.

    Args:
        df: DataFrame to log information about
        data_type: Type of data being logged
    """
    from .constants import DATA_TYPE_DESCRIPTIONS
    description = DATA_TYPE_DESCRIPTIONS.get(data_type, data_type)
    
    logger.info(f"\nValidated {description} dataframe summary:")
    logger.info(f"Shape: {df.shape}")
    logger.info(f"Columns: {df.columns.tolist()}")
    logger.debug(f"First few rows:\n{df.head().to_string()}")

    # Log missing values if any exist
    missing_values = df.isnull().sum()
    if missing_values.any():
        logger.warning(f"Missing values in {description}:")
        for col, count in missing_values[missing_values > 0].items():
            logger.warning(f"  {col}: {count} missing values")

def check_required_columns(
    df: pd.DataFrame,
    required_columns: List[str],
    data_type: str
) -> bool:
    """
    Check if DataFrame has all required columns.

    Args:
        df: DataFrame to check
        required_columns: List of required column names
        data_type: Type of data being checked

    Returns:
        True if all required columns are present, False otherwise
    """
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        logger.error(f"Missing columns in {data_type} data: {missing_columns}")
        return False
    return True
