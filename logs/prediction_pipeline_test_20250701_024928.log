2025-07-01 02:49:28,399 - __main__ - INFO - ================================================================================
2025-07-01 02:49:28,399 - __main__ - INFO - 🧪 PREDICTION PIPELINE COMPREHENSIVE TEST
2025-07-01 02:49:28,399 - __main__ - INFO - ================================================================================
2025-07-01 02:49:28,399 - __main__ - INFO - Log file: logs/prediction_pipeline_test_20250701_024928.log
2025-07-01 02:49:28,399 - __main__ - INFO - Test started at: 2025-07-01 02:49:28.399596
2025-07-01 02:49:28,399 - __main__ - INFO - 
==================================================
2025-07-01 02:49:28,399 - __main__ - INFO - 📊 STEP 1: Testing Data Loading
2025-07-01 02:49:28,399 - __main__ - INFO - ==================================================
2025-07-01 02:49:28,472 - data_loading.utils - INFO - Found 314 available leagues: SOUTH_AFRICA_PREMIERSHIP, ECUADOR_SERIE_B, ENGLAND_PREMIER_LEAGUE, INDONESIA_LIGA_1, GERMANY_OBERLIGA_HESSEN, TURKEY_SUPER_LIG, ITALY_SERIE_C_GROUP_C, FRANCE_NATIONAL_2_GROUP_B, BRAZIL_CATARINENSE, BELARUS_FIRST_LEAGUE, PHILIPPINES_FOOTBALL_LEAGUE, SLOVENIA_PRVA_LIGA, SPAIN_SEGUNDA_RFEF_GROUP_5, SCOTLAND_LEAGUE_ONE, FINLAND_KANSALLINEN_LIIGA_WOMEN, SCOTLAND_PREMIERSHIP, BRAZIL_SERGIPANO, SPAIN_PRIMERA_RFEF_GROUP_1, USA_USL_CHAMPIONSHIP, AUSTRALIA_WESTERN_AUSTRALIA_NPL, ENGLAND_CHAMPIONSHIP, ITALY_SERIE_A, AUSTRALIA_NORTHERN_NSW_NPL, SWEDEN_DIV_2_NORRA_GOTALAND, NORWAY_DIVISION_3_GROUP_2, CHINA_SUPER_LEAGUE, FRANCE_NATIONAL_2_GROUP_A, SWEDEN_DIV_2_SODRA_GOTALAND, AUSTRALIA_QUEENSLAND_NPL, NORWAY_1_DIVISION, LUXEMBOURG_NATIONAL_DIVISION, SWEDEN_SUPERETTAN, SPAIN_SEGUNDA_RFEF_GROUP_3, HUNGARY_NB_II, CZECH_REPUBLIC_1_LIGA_WOMEN, PERU_PRIMERA_DIVISION, SOUTH_KOREA_WK_LEAGUE_WOMEN, ITALY_SERIE_C_GROUP_B, VIETNAM_V_LEAGUE_2, UZBEKISTAN_SUPER_LEAGUE, QATAR_STARS_LEAGUE, IRAN_PERSIAN_GULF_PRO_LEAGUE, SERBIA_PRVA_LIGA, IRELAND_PREMIER_DIVISION, COSTA_RICA_PRIMERA_DIVISION, BELGIUM_U21_PRO_LEAGUE, SWEDEN_DIV_1_SODRA, SPAIN_SEGUNDA_RFEF_GROUP_4, BRAZIL_MINEIRO, NORTHERN_IRELAND_CHAMPIONSHIP, FINLAND_KAKKONEN_GROUP_B, NETHERLANDS_EERSTE_DIVISIE, NICARAGUA_PRIMERA_DIVISION, BRAZIL_BRASILIENSE, SPAIN_LA_LIGA, AUSTRIA_BUNDESLIGA, NORTH_MACEDONIA_FIRST_LEAGUE, LATVIA_1_LIGA, FAROE_ISLANDS_PREMIER_LEAGUE, ENGLAND_U21_PREMIER_LEAGUE, ENGLAND_NATIONAL_LEAGUE, ETHIOPIA_PREMIER_LEAGUE, BRAZIL_BRASILEIRO_WOMEN, BRAZIL_MATOGROSSENSE, ITALY_SERIE_D_GROUP_G, ZAMBIA_SUPER_LEAGUE, TURKEY_3_LIG_GROUP_1, GERMANY_OBERLIGA_HAMBURG, ISRAEL_NATIONAL_LEAGUE, GERMANY_REGIONALLIGA_NORDOST, ARMENIA_PREMIER_LEAGUE, CYPRUS_FIRST_DIVISION, BULGARIA_FIRST_LEAGUE, ISRAEL_LEUMIT_LEAGUE, GEORGIA_EROVNULI_LIGA, CROATIA_DRUGA_HNL, BRAZIL_PAULISTA_A3, PERU_SEGUNDA_DIVISION, URUGUAY_SEGUNDA_DIVISION, ICELAND_2_DEILD, SCOTLAND_LEAGUE_TWO, ENGLAND_PROFESSIONAL_DEVELOPMENT_LEAGUE, NORWAY_DIVISION_3_GROUP_5, ICELAND_URVALSDEILD_WOMEN, SPAIN_SEGUNDA_RFEF_GROUP_2, BANGLADESH_PREMIER_LEAGUE, WALES_PREMIER_LEAGUE, GEORGIA_LIGA_3, MOLDOVA_NATIONAL_DIVISION, DENMARK_1ST_DIVISION, IRELAND_FIRST_DIVISION, MONTENEGRO_FIRST_LEAGUE, MALAYSIA_SUPER_LEAGUE, SAUDI_ARABIA_DIVISION_1, BRAZIL_SERIE_B, SPAIN_SEGUNDA_RFEF_GROUP_1, CROATIA_PRVA_NL, ENGLAND_ISTHMIAN_LEAGUE, ITALY_SERIE_B, BRAZIL_PERNAMBUCANO, SLOVAKIA_2._LIGA, LITHUANIA_I_LYGA, BOSNIA_AND_HERZEGOVINA_PREMIER_LEAGUE, AUSTRALIA_NEW_SOUTH_WALES_NPL, ITALY_PRIMAVERA_1, POLAND_2_LIGA, ALGERIA_U21_LEAGUE, FRANCE_NATIONAL, KUWAIT_PREMIER_LEAGUE, AUSTRALIA_A_LEAGUE, PARAGUAY_DIVISION_INTERMEDIA, GERMANY_REGIONALLIGA_WEST, PARAGUAY_PRIMERA_DIVISION, SLOVAKIA_FORTUNA_LIGA, GEORGIA_EROVNULI_LIGA_2, BOLIVIA_LIGA_DE_FUTBOL_PROFESIONAL_BOLIVIANO, IRELAND_WOMENS_NATIONAL_LEAGUE, HONDURAS_LIGA_NACIONAL, ITALY_SERIE_D_GROUP_A, THAILAND_THAI_LEAGUE_2, ICELAND_1_DEILD, ITALY_SERIE_D_GROUP_D, ITALY_SERIE_D_GROUP_H, CHINA_LEAGUE_ONE, SWEDEN_DIV_1_NORRA, NORWAY_TOPPSERIEN_WOMEN, AUSTRIA_REGIONALLIGA_OST, EGYPT_PREMIER_LEAGUE, ITALY_SERIE_A_WOMEN, SWEDEN_ELITETTAN_WOMEN, ENGLAND_LEAGUE_1, ENGLAND_NATIONAL_LEAGUE_SOUTH, GERMANY_OBERLIGA_NIEDERSACHSEN, GERMANY_2_BUNDESLIGA_WOMEN, MAURITIUS_LIGUE_PROFESSIONNELLE, GERMANY_3_LIGA, NIGERIA_PROFESSIONAL_LEAGUE, GERMANY_OBERLIGA_BREMEN, SWEDEN_DIV_2_NORRA_SVEALAND, MEXICO_ASCENSO_MX, GREECE_SUPER_LEAGUE, ESTONIA_ESILIIGA, PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_D, FRANCE_LIGUE_1, PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_C, ICELAND_URVALSDEILD, SWITZERLAND_SUPER_LEAGUE, GHANA_PREMIER_LEAGUE, DENMARK_2ND_DIVISION, SWEDEN_ALLSVENSKAN, GUATEMALA_LIGA_NACIONAL, AUSTRIA_BUNDESLIGA_WOMEN, AUSTRIA_2_LIGA, BRAZIL_GAUCHO, ITALY_SERIE_D_GROUP_C, JAPAN_J3_LEAGUE, ITALY_SERIE_C_GROUP_A, NORWAY_DIVISION_2_GROUP_2, SINGAPORE_S.LEAGUE, SPAIN_PRIMERA_RFEF_GROUP_2, GIBRALTAR_PREMIER_DIVISION, CZECH_REPUBLIC_FNL, MEXICO_LIGA_MX, BAHRAIN_PREMIER_LEAGUE, BRAZIL_SERIE_A, ITALY_SERIE_D_GROUP_F, GERMANY_OBERLIGA_SCHLESWIG_HOLSTEIN, MALTA_PREMIER_LEAGUE, FRANCE_NATIONAL_2_GROUP_C, NORWAY_DIVISION_3_GROUP_1, BELGIUM_FIRST_DIVISION_B, GUATEMALA_PRIMERA_DIVISION, ISRAEL_LIGA_ALEF, BELGIUM_FIRST_DIVISION_A, BULGARIA_VTORA_LIGA, CHILE_PRIMERA_B, JAPAN_NADESHIKO_LEAGUE_1, GERMANY_OBERLIGA_RHEINLAND_PFALZ_SAAR, ARGENTINA_PRIMERA_NACIONAL, LATVIA_VIRSLIGA, AUSTRALIA_VICTORIA_NPL, PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_A, LITHUANIA_A_LYGA, PORTUGAL_LIGA_NOS, KOSOVO_SUPERLEAGUE, NETHERLANDS_EREDIVISIE, GERMANY_REGIONALLIGA_SUDWEST, SPAIN_SEGUNDA_DIVISION, ICELAND_1_DEILD_WOMEN, GERMANY_OBERLIGA_BAYERN_SUD, SOUTH_KOREA_K_LEAGUE_2, SWEDEN_ALLSVENSKAN_WOMEN, FINLAND_VEIKKAUSLIIGA, AUSTRIA_REGIONALLIGA_MITTE, VENEZUELA_PRIMERA_DIVISION, PANAMA_LIGA_PROM, COLOMBIA_PRIMERA_A, CZECH_REPUBLIC_FIRST_LEAGUE, FINLAND_YKKONEN, NETHERLANDS_DERDE_DIVISIE_SATURDAY, SPAIN_PRIMERA_F_WOMEN, TURKEY_3_LIG_GROUP_2, TURKEY_3_LIG_GROUP_4, INDIA_SUPER_LEAGUE, ITALY_SERIE_D_GROUP_E, SAUDI_ARABIA_PROFESSIONAL_LEAGUE, USA_NISA, NETHERLANDS_EREDIVISIE_WOMEN, ISRAEL_LIGA_ALEF_SOUTH, FINLAND_KAKKONEN_GROUP_C, INDIA_I-LEAGUE, ITALY_SERIE_D_GROUP_B, PANAMA_LIGA_PANAMENA_DE_FUTBOL, JAPAN_J1_LEAGUE, SERBIA_SUPERLIGA, FINLAND_KAKKONEN_GROUP_A, SCOTLAND_SWPL_1_WOMEN, GERMANY_OBERLIGA_MITTELRHEIN, AZERBAIJAN_PREMIER_LEAGUE, ZIMBABWE_PREMIER_SOCCER_LEAGUE, AUSTRALIA_SOUTH_AUSTRALIA_NPL, BRAZIL_PAULISTA_A2, FRANCE_LIGUE_2, ENGLAND_WOMENS_SUPER_LEAGUE, PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_B, ALBANIA_SUPERLIGA, URUGUAY_PRIMERA_DIVISION, SOUTH_KOREA_K_LEAGUE_1, ENGLAND_LEAGUE_2, HONG_KONG_PREMIER_LEAGUE, FAROE_ISLANDS_1_DEILD, VIETNAM_V_LEAGUE_1, NORWAY_ELITESERIEN, ITALY_SERIE_D_GROUP_I, GERMANY_BUNDESLIGA, ROMANIA_LIGA_I, GERMANY_OBERLIGA_BADEN_WURTTEMBERG, SWEDEN_DIV_2_SODRA_SVEALAND, MALTA_FIRST_DIVISION, USA_NWSL, USA_USL_LEAGUE_ONE, PORTUGAL_FIRST_DIVISION_WOMEN, CROATIA_PRVA_HNL, MOROCCO_BOTALA, NORWAY_DIVISION_3_GROUP_4, ARGENTINA_PRIMERA_DIVISION, ENGLAND_NATIONAL_LEAGUE_NORTH, AUSTRALIA_CAPITAL_TERRITORY_NPL, UKRAINE_PREMIER_LEAGUE, POLAND_I_LIGA, GERMANY_BUNDESLIGA_WOMEN, BRAZIL_SERIE_D, NETHERLANDS_TWEEDE_DIVISIE, BRAZIL_TOCANTINENSE, GERMANY_REGIONALLIGA_BAYERN, PORTUGAL_LIGA_PRO, GERMANY_OBERLIGA_WESTFALEN, BRAZIL_PARABIANO, GERMANY_OBERLIGA_NIEDERRHEIN, CHILE_PRIMERA_DIVISION, ITALY_PRIMAVERA_2, MYANMAR_NATIONAL_LEAGUE, NORWAY_DIVISION_2_GROUP_1, FRANCE_FEMININE_DIVISION_1, SWEDEN_DIV_2_NORRLAND, USA_MAJOR_LEAGUE_SOCCER, AUSTRALIA_TASMANIA_NPL, SPAIN_LIGA_F_WOMEN, SOUTH_KOREA_K3_LEAGUE, RUSSIA_FNL, ESTONIA_ESILIIGA_B, KAZAKHSTAN_PREMIER_LEAGUE, ESTONIA_MEISTRILIIGA, NORWAY_DIVISION_3_GROUP_3, QATAR_DIVISION_2, BELGIUM_NATIONAL_DIVISION_1, TAJIKISTAN_VYSSHAYA_LIGA, BELARUS_VYSSHAYA_LIGA, SWEDEN_DIV_2_VASTRA_GOTALAND, NORWAY_1_DIVISION_WOMEN, CANADA_PREMIER_LEAGUE, GERMANY_REGIONALLIGA_NORD, HUNGARY_NB_I, RUSSIA_PREMIER_LEAGUE, BRAZIL_SERIE_C, ICELAND_3_DEILD, FINLAND_YKKOSLIIGA, JAPAN_J2_LEAGUE, NORWAY_DIVISION_3_GROUP_6, ANDORRA_PRIMERA_DIVISIO, TURKEY_1_LIG, AUSTRALIA_A_LEAGUE_WOMEN, CZECH_REPUBLIC_U19_LEAGUE, THAILAND_THAI_LEAGUE_1, COSTA_RICA_LIGA_DE_ASCENSO, POLAND_EKSTRALIGA_WOMEN, NORTHERN_IRELAND_PREMIERSHIP, POLAND_EKSTRAKLASA, SCOTLAND_CHAMPIONSHIP, GERMANY_OBERLIGA_BAYERN_NORD, ECUADOR_SERIE_A, NORTHERN_IRELAND_PREMIERSHIP_WOMEN, BRAZIL_PAULISTA_A1, DENMARK_SUPERLIGA, ISRAEL_LIGA_ALEF_NORTH, ISRAEL_PREMIER_LEAGUE, TURKEY_3_LIG_GROUP_3, SWITZERLAND_WOMEN_SUPER_LEAGUE, GERMANY_BUNDESLIGA_2, JAPAN_WE_LEAGUE, ENGLAND_WOMENS_CHAMPIONSHIP, CYPRUS_SECOND_DIVISION, SWITZERLAND_CHALLENGE_LEAGUE, BRAZIL_GOIANO
2025-07-01 02:49:28,473 - __main__ - INFO - Available leagues: 314
2025-07-01 02:49:28,473 - __main__ - DEBUG - League list: ['SOUTH_AFRICA_PREMIERSHIP', 'ECUADOR_SERIE_B', 'ENGLAND_PREMIER_LEAGUE', 'INDONESIA_LIGA_1', 'GERMANY_OBERLIGA_HESSEN', 'TURKEY_SUPER_LIG', 'ITALY_SERIE_C_GROUP_C', 'FRANCE_NATIONAL_2_GROUP_B', 'BRAZIL_CATARINENSE', 'BELARUS_FIRST_LEAGUE']...
2025-07-01 02:49:28,473 - __main__ - INFO - ✅ League configuration loaded for ENGLAND_PREMIER_LEAGUE
2025-07-01 02:49:28,473 - __main__ - DEBUG - Config keys: ['LEAGUE_STATS_URL', 'LEAGUE_TABLE_URL', 'TEAM_URLS', 'HEAD_TO_HEAD_URLS', 'TEAM_NAME_MAPPING', 'CURRENT_LEAGUE']
2025-07-01 02:49:28,473 - __main__ - INFO - Loading data files...
2025-07-01 02:49:28,473 - data_loading.core - INFO - Loading data for league: ENGLAND_PREMIER_LEAGUE
2025-07-01 02:49:28,477 - data_loading.validation - INFO - Validating results dataframe with columns: ['Date', 'Home Team', 'Score', 'Away Team', 'Result', 'Team']
2025-07-01 02:49:28,519 - data_loading.utils - INFO - 
Validated match results dataframe summary:
2025-07-01 02:49:28,519 - data_loading.utils - INFO - Shape: (760, 8)
2025-07-01 02:49:28,519 - data_loading.utils - INFO - Columns: ['Date', 'Home Team', 'Score', 'Away Team', 'Result', 'Team', 'Home Score', 'Away Score']
2025-07-01 02:49:28,523 - data_loading.utils - INFO - First few rows:
        Date        Home Team  Score      Away Team  Result     Team  Home Score  Away Score
0 2025-08-17          Arsenal  2 - 0  Wolverhampton       0  Arsenal           2           0
1 2025-08-24      Aston Villa  0 - 2        Arsenal       2  Arsenal           0           2
2 2025-08-31          Arsenal  1 - 1       Brighton       1  Arsenal           1           1
3 2025-09-15        Tottenham  0 - 1        Arsenal       2  Arsenal           0           1
4 2025-09-22  Manchester City  2 - 2        Arsenal       1  Arsenal           2           2
2025-07-01 02:49:28,524 - data_loading.core - INFO - Loaded results dataframe with shape: (760, 8)
2025-07-01 02:49:28,527 - data_loading.validation - INFO - Validating team_stats dataframe with columns: ['Team', 'total_home_played', 'total_home_wins', 'total_home_draws', 'total_home_losses', 'total_away_played', 'total_away_wins', 'total_away_draws', 'total_away_losses', 'total_played', 'total_wins', 'total_draws', 'total_losses', 'points_per_game', 'home_points_per_game', 'away_points_per_game', 'goals_scored_home', 'goals_scored_away', 'goals_scored_all', 'goals_scored_per_match_home', 'goals_scored_per_match_away', 'goals_scored_per_match_all', 'goals_conceded_home', 'goals_conceded_away', 'goals_conceded_all', 'goals_conceded_per_match_home', 'goals_conceded_per_match_away', 'goals_conceded_per_match_all', 'gf_ga_per_match_home', 'gf_ga_per_match_away', 'gf_ga_per_match_all', 'gf_ga_over_0_5_percentage_home', 'gf_ga_over_0_5_percentage_away', 'gf_ga_over_0_5_percentage_all', 'gf_ga_over_1_5_percentage_home', 'gf_ga_over_1_5_percentage_away', 'gf_ga_over_1_5_percentage_all', 'gf_ga_over_2_5_percentage_home', 'gf_ga_over_2_5_percentage_away', 'gf_ga_over_2_5_percentage_all', 'gf_ga_over_3_5_percentage_home', 'gf_ga_over_3_5_percentage_away', 'gf_ga_over_3_5_percentage_all', 'gf_ga_over_4_5_percentage_home', 'gf_ga_over_4_5_percentage_away', 'gf_ga_over_4_5_percentage_all', 'gf_ga_over_5_5_percentage_home', 'gf_ga_over_5_5_percentage_away', 'gf_ga_over_5_5_percentage_all', 'gf_ga_over_0_5_ht_percentage_home', 'gf_ga_over_0_5_ht_percentage_away', 'gf_ga_over_0_5_ht_percentage_all', 'gf_ga_over_1_5_ht_percentage_home', 'gf_ga_over_1_5_ht_percentage_away', 'gf_ga_over_1_5_ht_percentage_all', 'gf_ga_over_2_5_ht_percentage_home', 'gf_ga_over_2_5_ht_percentage_away', 'gf_ga_over_2_5_ht_percentage_all', 'ppg_last_8', 'avg_goals_scored_last_8', 'avg_goals_conceded_last_8', 'total_points']
2025-07-01 02:49:28,538 - data_loading.utils - INFO - 
Validated team statistics dataframe summary:
2025-07-01 02:49:28,538 - data_loading.utils - INFO - Shape: (20, 62)
2025-07-01 02:49:28,538 - data_loading.utils - INFO - Columns: ['Team', 'total_home_played', 'total_home_wins', 'total_home_draws', 'total_home_losses', 'total_away_played', 'total_away_wins', 'total_away_draws', 'total_away_losses', 'total_played', 'total_wins', 'total_draws', 'total_losses', 'points_per_game', 'home_points_per_game', 'away_points_per_game', 'goals_scored_home', 'goals_scored_away', 'goals_scored_all', 'goals_scored_per_match_home', 'goals_scored_per_match_away', 'goals_scored_per_match_all', 'goals_conceded_home', 'goals_conceded_away', 'goals_conceded_all', 'goals_conceded_per_match_home', 'goals_conceded_per_match_away', 'goals_conceded_per_match_all', 'gf_ga_per_match_home', 'gf_ga_per_match_away', 'gf_ga_per_match_all', 'gf_ga_over_0_5_percentage_home', 'gf_ga_over_0_5_percentage_away', 'gf_ga_over_0_5_percentage_all', 'gf_ga_over_1_5_percentage_home', 'gf_ga_over_1_5_percentage_away', 'gf_ga_over_1_5_percentage_all', 'gf_ga_over_2_5_percentage_home', 'gf_ga_over_2_5_percentage_away', 'gf_ga_over_2_5_percentage_all', 'gf_ga_over_3_5_percentage_home', 'gf_ga_over_3_5_percentage_away', 'gf_ga_over_3_5_percentage_all', 'gf_ga_over_4_5_percentage_home', 'gf_ga_over_4_5_percentage_away', 'gf_ga_over_4_5_percentage_all', 'gf_ga_over_5_5_percentage_home', 'gf_ga_over_5_5_percentage_away', 'gf_ga_over_5_5_percentage_all', 'gf_ga_over_0_5_ht_percentage_home', 'gf_ga_over_0_5_ht_percentage_away', 'gf_ga_over_0_5_ht_percentage_all', 'gf_ga_over_1_5_ht_percentage_home', 'gf_ga_over_1_5_ht_percentage_away', 'gf_ga_over_1_5_ht_percentage_all', 'gf_ga_over_2_5_ht_percentage_home', 'gf_ga_over_2_5_ht_percentage_away', 'gf_ga_over_2_5_ht_percentage_all', 'ppg_last_8', 'avg_goals_scored_last_8', 'avg_goals_conceded_last_8', 'total_points']
2025-07-01 02:49:28,555 - data_loading.utils - INFO - First few rows:
          Team  total_home_played  total_home_wins  total_home_draws  total_home_losses  total_away_played  total_away_wins  total_away_draws  total_away_losses  total_played  total_wins  total_draws  total_losses  points_per_game  home_points_per_game  away_points_per_game  goals_scored_home  goals_scored_away  goals_scored_all  goals_scored_per_match_home  goals_scored_per_match_away  goals_scored_per_match_all  goals_conceded_home  goals_conceded_away  goals_conceded_all  goals_conceded_per_match_home  goals_conceded_per_match_away  goals_conceded_per_match_all  gf_ga_per_match_home  gf_ga_per_match_away  gf_ga_per_match_all  gf_ga_over_0_5_percentage_home  gf_ga_over_0_5_percentage_away  gf_ga_over_0_5_percentage_all  gf_ga_over_1_5_percentage_home  gf_ga_over_1_5_percentage_away  gf_ga_over_1_5_percentage_all  gf_ga_over_2_5_percentage_home  gf_ga_over_2_5_percentage_away  gf_ga_over_2_5_percentage_all  gf_ga_over_3_5_percentage_home  gf_ga_over_3_5_percentage_away  gf_ga_over_3_5_percentage_all  gf_ga_over_4_5_percentage_home  gf_ga_over_4_5_percentage_away  gf_ga_over_4_5_percentage_all  gf_ga_over_5_5_percentage_home  gf_ga_over_5_5_percentage_away  gf_ga_over_5_5_percentage_all  gf_ga_over_0_5_ht_percentage_home  gf_ga_over_0_5_ht_percentage_away  gf_ga_over_0_5_ht_percentage_all  gf_ga_over_1_5_ht_percentage_home  gf_ga_over_1_5_ht_percentage_away  gf_ga_over_1_5_ht_percentage_all  gf_ga_over_2_5_ht_percentage_home  gf_ga_over_2_5_ht_percentage_away  gf_ga_over_2_5_ht_percentage_all  ppg_last_8  avg_goals_scored_last_8  avg_goals_conceded_last_8  total_points
0      Arsenal                 19               11                 6                  2                 19                9                 8                  2            38          20           14             4         1.947368                  2.05                  1.84                 35                 34                69                         1.84                         1.79                        1.82                   17                   17                  34                           0.89                           0.89                          0.89                  2.74                  2.68                 2.71                            95.0                            95.0                           95.0                            74.0                            79.0                           76.0                            53.0                            37.0                           45.0                            32.0                            32.0                           32.0                            11.0                            11.0                           11.0                            11.0                            11.0                           11.0                               74.0                               63.0                              68.0                               21.0                               32.0                              26.0                               16.0                               16.0                              16.0    1.947368                     1.82                       0.89            74
1  Aston Villa                 19               11                 7                  1                 19                8                 2                  9            38          19            9            10         1.736842                  2.11                  1.37                 34                 24                58                         1.79                         1.26                        1.53                   20                   31                  51                           1.05                           1.63                          1.34                  2.84                  2.89                 2.87                            95.0                           100.0                           97.0                            84.0                            84.0                           84.0                            58.0                            68.0                           63.0                            37.0                            26.0                           32.0                            11.0                            11.0                           11.0                             0.0                             0.0                            0.0                               68.0                               68.0                              68.0                               37.0                               26.0                              32.0                               21.0                                5.0                              13.0    1.736842                     1.53                       1.34            66
2  Bournemouth                 19                8                 4                  7                 19                7                 7                  5            38          15           11            12         1.473684                  1.47                  1.47                 23                 35                58                         1.21                         1.84                        1.53                   16                   30                  46                           0.84                           1.58                          1.21                  2.05                  3.42                 2.74                            95.0                            95.0                           95.0                            63.0                            89.0                           76.0                            32.0                            79.0                           55.0                            11.0                            53.0                           32.0                             5.0                            21.0                           13.0                             0.0                             5.0                            3.0                               68.0                               84.0                              76.0                               11.0                               32.0                              21.0                                5.0                               16.0                              11.0    1.473684                     1.53                       1.21            56
3    Brentford                 19                9                 4                  6                 19                7                 4                  8            38          16            8            14         1.473684                  1.63                  1.32                 40                 26                66                         2.11                         1.37                        1.74                   35                   22                  57                           1.84                           1.16                          1.50                  3.95                  2.53                 3.24                            95.0                            89.0                           92.0                            89.0                            79.0                           84.0                            63.0                            58.0                           61.0                            58.0                            21.0                           39.0                            42.0                             5.0                           24.0                            26.0                             0.0                           13.0                               79.0                               79.0                              79.0                               47.0                               26.0                              37.0                               32.0                               21.0                              26.0    1.473684                     1.74                       1.50            56
4     Brighton                 19                8                 8                  3                 19                8                 5                  6            38          16           13             9         1.605263                  1.68                  1.53                 30                 36                66                         1.58                         1.89                        1.74                   26                   33                  59                           1.37                           1.74                          1.55                  2.95                  3.63                 3.29                            89.0                           100.0                           95.0                            84.0                            95.0                           89.0                            68.0                            74.0                           71.0                            37.0                            53.0                           45.0                            16.0                            21.0                           18.0                             0.0                            16.0                            8.0                               84.0                               89.0                              87.0                               37.0                               37.0                              37.0                               11.0                               16.0                              13.0    1.605263                     1.74                       1.55            61
2025-07-01 02:49:28,558 - data_loading.core - INFO - Loaded team_stats dataframe with shape: (20, 62)
2025-07-01 02:49:28,559 - data_loading.validation - INFO - Validating league_stats dataframe with columns: ['Stat', 'Value']
2025-07-01 02:49:28,559 - data_loading.utils - INFO - 
Validated league statistics dataframe summary:
2025-07-01 02:49:28,559 - data_loading.utils - INFO - Shape: (30, 2)
2025-07-01 02:49:28,559 - data_loading.utils - INFO - Columns: ['Stat', 'Value']
2025-07-01 02:49:28,561 - data_loading.utils - INFO - First few rows:
                   Stat  Value
0   avg_goals_per_match   2.93
1  home_goals_per_match   1.51
2  away_goals_per_match   1.42
3   home_win_percentage  41.00
4       draw_percentage  24.00
2025-07-01 02:49:28,561 - data_loading.core - INFO - Loaded league_stats dataframe with shape: (30, 2)
2025-07-01 02:49:28,566 - data_loading.validation - INFO - Validating h2h_stats dataframe with columns: ['Matchup', 'home_team_name', 'away_team_name', 'total_matches', 'home_win_percentage', 'away_win_percentage', 'draw_percentage', 'home_wins', 'away_wins', 'draws', 'home_goals', 'away_goals', 'over_1_5_percentage', 'over_2_5_percentage', 'over_3_5_percentage', 'btts_percentage', 'home_clean_sheet_percentage', 'away_clean_sheet_percentage', 'recent_results']
2025-07-01 02:49:28,570 - data_loading.utils - INFO - 
Validated head-to-head statistics dataframe summary:
2025-07-01 02:49:28,570 - data_loading.utils - INFO - Shape: (184, 19)
2025-07-01 02:49:28,570 - data_loading.utils - INFO - Columns: ['Matchup', 'home_team_name', 'away_team_name', 'total_matches', 'home_win_percentage', 'away_win_percentage', 'draw_percentage', 'home_wins', 'away_wins', 'draws', 'home_goals', 'away_goals', 'over_1_5_percentage', 'over_2_5_percentage', 'over_3_5_percentage', 'btts_percentage', 'home_clean_sheet_percentage', 'away_clean_sheet_percentage', 'recent_results']
2025-07-01 02:49:28,577 - data_loading.utils - INFO - First few rows:
                                      Matchup  home_team_name  away_team_name  total_matches  home_win_percentage  away_win_percentage  draw_percentage  home_wins  away_wins  draws  home_goals  away_goals  over_1_5_percentage  over_2_5_percentage  over_3_5_percentage  btts_percentage  home_clean_sheet_percentage  away_clean_sheet_percentage                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             recent_results
0               Afc Bournemouth vs Arsenal Fc             NaN             NaN             17                 71.0                 12.0             17.0         12          2      3          39          15                 94.0                 65.0                 29.0             53.0                         41.0                          6.0                                                                                                                                                                                     Oct 19, 2024: AFC Bournemouth2 vs Arsenal0 | Jul 25, 2024: Arsenal1 vs AFC Bournemouth1 | May 4, 2024: Arsenal3 vs AFC Bournemouth0 | Sep 30, 2023: AFC Bournemouth0 vs Arsenal4 | Mar 4, 2023: Arsenal3 vs AFC Bournemouth2 | Aug 20, 2022: AFC Bournemouth0 vs Arsenal3 | Jan 27, 2020: AFC Bournemouth1 vs Arsenal2 | Dec 26, 2019: AFC Bournemouth1 vs Arsenal1 | Oct 6, 2019: Arsenal1 vs AFC Bournemouth0 | Feb 27, 2019: Arsenal5 vs AFC Bournemouth1 | Nov 25, 2018: AFC Bournemouth1 vs Arsenal2 | Jan 14, 2018: AFC Bournemouth2 vs Arsenal1
1           Afc Bournemouth vs Aston Villa Fc             NaN             NaN             10                 40.0                 40.0             20.0          4          4      2          13          15                 90.0                 70.0                 20.0             70.0                         10.0                         20.0                                                                                                                                                                                                                                         Oct 26, 2024: Aston Villa1 vs AFC Bournemouth1 | Apr 21, 2024: Aston Villa3 vs AFC Bournemouth1 | Dec 3, 2023: AFC Bournemouth2 vs Aston Villa2 | Mar 18, 2023: Aston Villa3 vs AFC Bournemouth0 | Aug 6, 2022: AFC Bournemouth2 vs Aston Villa0 | Feb 1, 2020: AFC Bournemouth2 vs Aston Villa1 | Aug 17, 2019: Aston Villa1 vs AFC Bournemouth2 | Apr 9, 2016: Aston Villa1 vs AFC Bournemouth2 | Aug 8, 2015: AFC Bournemouth0 vs Aston Villa1 | Jan 25, 2015: Aston Villa2 vs AFC Bournemouth1
2             Afc Bournemouth vs Brentford Fc             NaN             NaN             18                 22.0                 44.0             34.0          4          8      6          18          29                 67.0                 50.0                 39.0             56.0                         28.0                         28.0                                                                                                                                                              Nov 9, 2024: Brentford3 vs AFC Bournemouth2 | May 11, 2024: AFC Bournemouth1 vs Brentford2 | Sep 2, 2023: Brentford2 vs AFC Bournemouth2 | Jan 14, 2023: Brentford2 vs AFC Bournemouth0 | Oct 1, 2022: AFC Bournemouth0 vs Brentford0 | May 22, 2021: Brentford3 vs AFC Bournemouth1 | May 17, 2021: AFC Bournemouth1 vs Brentford0 | Apr 24, 2021: AFC Bournemouth0 vs Brentford1 | Dec 30, 2020: Brentford2 vs AFC Bournemouth1 | Feb 21, 2015: Brentford3 vs AFC Bournemouth1 | Aug 16, 2014: AFC Bournemouth1 vs Brentford0 | Jan 1, 2013: Brentford0 vs AFC Bournemouth0
3  Afc Bournemouth vs Brighton Hove Albion Fc             NaN             NaN             19                 32.0                 47.0             21.0          6          9      4          22          30                 84.0                 47.0                 32.0             53.0                         16.0                         32.0  Nov 23, 2024: AFC Bournemouth1 vs Brighton & Hove Albion2 | Apr 28, 2024: AFC Bournemouth3 vs Brighton & Hove Albion0 | Sep 24, 2023: Brighton & Hove Albion3 vs AFC Bournemouth1 | Apr 4, 2023: AFC Bournemouth0 vs Brighton & Hove Albion2 | Feb 4, 2023: Brighton & Hove Albion1 vs AFC Bournemouth0 | Jan 21, 2020: AFC Bournemouth3 vs Brighton & Hove Albion1 | Dec 28, 2019: Brighton & Hove Albion2 vs AFC Bournemouth0 | Apr 13, 2019: Brighton & Hove Albion0 vs AFC Bournemouth5 | Jan 5, 2019: AFC Bournemouth1 vs Brighton & Hove Albion3 | Dec 22, 2018: AFC Bournemouth2 vs Brighton & Hove Albion0 | Jan 1, 2018: Brighton & Hove Albion2 vs AFC Bournemouth2 | Sep 19, 2017: AFC Bournemouth1 vs Brighton & Hove Albion0
4               Afc Bournemouth vs Chelsea Fc             NaN             NaN             17                 65.0                 24.0             11.0         11          4      2          26          16                 65.0                 53.0                 29.0             35.0                         41.0                         29.0                                                                                                                                                                                    Sep 14, 2024: AFC Bournemouth0 vs Chelsea1 | May 19, 2024: Chelsea2 vs AFC Bournemouth1 | Sep 17, 2023: AFC Bournemouth0 vs Chelsea0 | May 6, 2023: AFC Bournemouth1 vs Chelsea3 | Dec 27, 2022: Chelsea2 vs AFC Bournemouth0 | Feb 29, 2020: AFC Bournemouth2 vs Chelsea2 | Dec 14, 2019: Chelsea0 vs AFC Bournemouth1 | Jan 30, 2019: AFC Bournemouth4 vs Chelsea0 | Dec 19, 2018: Chelsea1 vs AFC Bournemouth0 | Sep 1, 2018: Chelsea2 vs AFC Bournemouth0 | Jan 31, 2018: Chelsea0 vs AFC Bournemouth3 | Dec 20, 2017: Chelsea2 vs AFC Bournemouth1
2025-07-01 02:49:28,579 - data_loading.utils - WARNING - Missing values in head-to-head statistics:
2025-07-01 02:49:28,579 - data_loading.utils - WARNING -   home_team_name: 184 missing values
2025-07-01 02:49:28,579 - data_loading.utils - WARNING -   away_team_name: 184 missing values
2025-07-01 02:49:28,579 - data_loading.core - INFO - Loaded h2h_stats dataframe with shape: (184, 19)
2025-07-01 02:49:28,581 - data_loading.validation - INFO - Validating league_table dataframe with columns: ['Position', 'Team', 'MP', 'W', 'D', 'L', 'GF', 'GA', 'GD', 'Pts']
2025-07-01 02:49:28,583 - data_loading.utils - INFO - 
Validated league table dataframe summary:
2025-07-01 02:49:28,583 - data_loading.utils - INFO - Shape: (20, 10)
2025-07-01 02:49:28,583 - data_loading.utils - INFO - Columns: ['Position', 'Team', 'MP', 'W', 'D', 'L', 'GF', 'GA', 'GD', 'Pts']
2025-07-01 02:49:28,586 - data_loading.utils - INFO - First few rows:
   Position                 Team  MP   W   D   L  GF  GA  GD  Pts
0         1         Liverpool FC  38  25   9   4  86  41  45   84
1         2           Arsenal FC  38  20  14   4  69  34  35   74
2         3   Manchester City FC  38  21   8   9  72  44  28   71
3         4           Chelsea FC  38  20   9   9  64  43  21   69
4         5  Newcastle United FC  38  20   6  12  68  47  21   66
2025-07-01 02:49:28,587 - data_loading.core - INFO - Loaded league_table dataframe with shape: (20, 10)
2025-07-01 02:49:28,587 - data_loading.core - INFO - Successfully loaded all data files for league: ENGLAND_PREMIER_LEAGUE
2025-07-01 02:49:28,587 - __main__ - INFO - ✅ Data loaded successfully:
2025-07-01 02:49:28,587 - __main__ - INFO -    - Results: 760 matches
2025-07-01 02:49:28,587 - __main__ - INFO -    - Team stats: 20 teams
2025-07-01 02:49:28,587 - __main__ - INFO -    - League stats: 30 statistics
2025-07-01 02:49:28,587 - __main__ - INFO -    - H2H stats: 184 matchups
2025-07-01 02:49:28,587 - __main__ - INFO -    - League table: 20 teams
2025-07-01 02:49:28,587 - __main__ - DEBUG - Sample results data:
2025-07-01 02:49:28,591 - __main__ - DEBUG - 
        Date        Home Team  Score      Away Team  Result     Team  Home Score  Away Score
0 2025-08-17          Arsenal  2 - 0  Wolverhampton       0  Arsenal           2           0
1 2025-08-24      Aston Villa  0 - 2        Arsenal       2  Arsenal           0           2
2 2025-08-31          Arsenal  1 - 1       Brighton       1  Arsenal           1           1
3 2025-09-15        Tottenham  0 - 1        Arsenal       2  Arsenal           0           1
4 2025-09-22  Manchester City  2 - 2        Arsenal       1  Arsenal           2           2
2025-07-01 02:49:28,591 - __main__ - DEBUG - Sample team stats data:
2025-07-01 02:49:28,612 - __main__ - DEBUG - 
          Team  total_home_played  total_home_wins  total_home_draws  total_home_losses  total_away_played  total_away_wins  total_away_draws  total_away_losses  total_played  total_wins  total_draws  total_losses  points_per_game  home_points_per_game  away_points_per_game  goals_scored_home  goals_scored_away  goals_scored_all  goals_scored_per_match_home  goals_scored_per_match_away  goals_scored_per_match_all  goals_conceded_home  goals_conceded_away  goals_conceded_all  goals_conceded_per_match_home  goals_conceded_per_match_away  goals_conceded_per_match_all  gf_ga_per_match_home  gf_ga_per_match_away  gf_ga_per_match_all  gf_ga_over_0_5_percentage_home  gf_ga_over_0_5_percentage_away  gf_ga_over_0_5_percentage_all  gf_ga_over_1_5_percentage_home  gf_ga_over_1_5_percentage_away  gf_ga_over_1_5_percentage_all  gf_ga_over_2_5_percentage_home  gf_ga_over_2_5_percentage_away  gf_ga_over_2_5_percentage_all  gf_ga_over_3_5_percentage_home  gf_ga_over_3_5_percentage_away  gf_ga_over_3_5_percentage_all  gf_ga_over_4_5_percentage_home  gf_ga_over_4_5_percentage_away  gf_ga_over_4_5_percentage_all  gf_ga_over_5_5_percentage_home  gf_ga_over_5_5_percentage_away  gf_ga_over_5_5_percentage_all  gf_ga_over_0_5_ht_percentage_home  gf_ga_over_0_5_ht_percentage_away  gf_ga_over_0_5_ht_percentage_all  gf_ga_over_1_5_ht_percentage_home  gf_ga_over_1_5_ht_percentage_away  gf_ga_over_1_5_ht_percentage_all  gf_ga_over_2_5_ht_percentage_home  gf_ga_over_2_5_ht_percentage_away  gf_ga_over_2_5_ht_percentage_all  ppg_last_8  avg_goals_scored_last_8  avg_goals_conceded_last_8  total_points
0      Arsenal                 19               11                 6                  2                 19                9                 8                  2            38          20           14             4         1.947368                  2.05                  1.84                 35                 34                69                         1.84                         1.79                        1.82                   17                   17                  34                           0.89                           0.89                          0.89                  2.74                  2.68                 2.71                            95.0                            95.0                           95.0                            74.0                            79.0                           76.0                            53.0                            37.0                           45.0                            32.0                            32.0                           32.0                            11.0                            11.0                           11.0                            11.0                            11.0                           11.0                               74.0                               63.0                              68.0                               21.0                               32.0                              26.0                               16.0                               16.0                              16.0    1.947368                     1.82                       0.89            74
1  Aston Villa                 19               11                 7                  1                 19                8                 2                  9            38          19            9            10         1.736842                  2.11                  1.37                 34                 24                58                         1.79                         1.26                        1.53                   20                   31                  51                           1.05                           1.63                          1.34                  2.84                  2.89                 2.87                            95.0                           100.0                           97.0                            84.0                            84.0                           84.0                            58.0                            68.0                           63.0                            37.0                            26.0                           32.0                            11.0                            11.0                           11.0                             0.0                             0.0                            0.0                               68.0                               68.0                              68.0                               37.0                               26.0                              32.0                               21.0                                5.0                              13.0    1.736842                     1.53                       1.34            66
2  Bournemouth                 19                8                 4                  7                 19                7                 7                  5            38          15           11            12         1.473684                  1.47                  1.47                 23                 35                58                         1.21                         1.84                        1.53                   16                   30                  46                           0.84                           1.58                          1.21                  2.05                  3.42                 2.74                            95.0                            95.0                           95.0                            63.0                            89.0                           76.0                            32.0                            79.0                           55.0                            11.0                            53.0                           32.0                             5.0                            21.0                           13.0                             0.0                             5.0                            3.0                               68.0                               84.0                              76.0                               11.0                               32.0                              21.0                                5.0                               16.0                              11.0    1.473684                     1.53                       1.21            56
3    Brentford                 19                9                 4                  6                 19                7                 4                  8            38          16            8            14         1.473684                  1.63                  1.32                 40                 26                66                         2.11                         1.37                        1.74                   35                   22                  57                           1.84                           1.16                          1.50                  3.95                  2.53                 3.24                            95.0                            89.0                           92.0                            89.0                            79.0                           84.0                            63.0                            58.0                           61.0                            58.0                            21.0                           39.0                            42.0                             5.0                           24.0                            26.0                             0.0                           13.0                               79.0                               79.0                              79.0                               47.0                               26.0                              37.0                               32.0                               21.0                              26.0    1.473684                     1.74                       1.50            56
4     Brighton                 19                8                 8                  3                 19                8                 5                  6            38          16           13             9         1.605263                  1.68                  1.53                 30                 36                66                         1.58                         1.89                        1.74                   26                   33                  59                           1.37                           1.74                          1.55                  2.95                  3.63                 3.29                            89.0                           100.0                           95.0                            84.0                            95.0                           89.0                            68.0                            74.0                           71.0                            37.0                            53.0                           45.0                            16.0                            21.0                           18.0                             0.0                            16.0                            8.0                               84.0                               89.0                              87.0                               37.0                               37.0                              37.0                               11.0                               16.0                              13.0    1.605263                     1.74                       1.55            61
2025-07-01 02:49:28,612 - __main__ - INFO - 
==================================================
2025-07-01 02:49:28,612 - __main__ - INFO - 🔧 STEP 2: Testing Feature Preparation
2025-07-01 02:49:28,613 - __main__ - INFO - ==================================================
2025-07-01 02:49:29,483 - __main__ - INFO - Preparing features...
2025-07-01 02:49:29,483 - feature_engineering.core - INFO - Starting feature preparation for 760 matches
2025-07-01 02:49:33,115 - feature_engineering.core - INFO - Processing 760 valid matches. Skipped 0 matches.
2025-07-01 02:49:33,115 - feature_engineering.core - INFO - Unique labels for three_way: ['Home' 'Away' 'Draw']
2025-07-01 02:49:33,117 - feature_engineering.core - INFO - Unique labels for over_under_1_5: ['Over 1.5' 'Under 1.5']
2025-07-01 02:49:33,118 - feature_engineering.core - INFO - Unique labels for over_under_2_5: ['Under 2.5' 'Over 2.5']
2025-07-01 02:49:33,119 - feature_engineering.core - INFO - Unique labels for over_under_3_5: ['Under 3.5' 'Over 3.5']
2025-07-01 02:49:33,120 - feature_engineering.core - INFO - Unique labels for btts: ['No' 'Yes']
2025-07-01 02:49:33,122 - __main__ - INFO - ✅ Features prepared successfully:
2025-07-01 02:49:33,122 - __main__ - INFO -    - Shape: (760, 46)
2025-07-01 02:49:33,122 - __main__ - INFO -    - Columns: 46
2025-07-01 02:49:33,122 - __main__ - DEBUG -    - Column names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength', 'home_form', 'away_form', 'home_recent_scoring_rate', 'away_recent_scoring_rate', 'home_recent_conceding_rate', 'away_recent_conceding_rate', 'home_team_position', 'away_team_position', 'position_difference', 'h2h_home_recent_win_rate', 'h2h_away_recent_win_rate', 'h2h_recent_draw_rate', 'h2h_team_a_win_percentage', 'h2h_team_b_win_percentage', 'h2h_draw_percentage', 'h2h_total_matches', 'h2h_team_a_goals', 'h2h_team_b_goals', 'h2h_btts_percentage', 'h2h_goal_pattern', 'Team', 'Home Team', 'Away Team', 'form_data_valid_str', 'form_data_valid', 'result', 'three_way', 'btts', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'three_way_encoded', 'over_under_1_5_encoded', 'over_under_2_5_encoded', 'over_under_3_5_encoded', 'btts_encoded']
2025-07-01 02:49:33,124 - __main__ - INFO - ✅ No missing values found
2025-07-01 02:49:33,125 - __main__ - INFO - ✅ X data shape: (760, 34)
2025-07-01 02:49:33,125 - __main__ - INFO - ✅ Y data targets: ['three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts']
2025-07-01 02:49:33,125 - __main__ - INFO - ✅ Label encoders: ['three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts']
2025-07-01 02:49:33,125 - __main__ - INFO - 
==================================================
2025-07-01 02:49:33,125 - __main__ - INFO - 🤖 STEP 3: Testing Model Training
2025-07-01 02:49:33,125 - __main__ - INFO - ==================================================
2025-07-01 02:49:35,279 - h5py._conv - DEBUG - Creating converter from 7 to 5
2025-07-01 02:49:35,280 - h5py._conv - DEBUG - Creating converter from 5 to 7
2025-07-01 02:49:35,280 - h5py._conv - DEBUG - Creating converter from 7 to 5
2025-07-01 02:49:35,280 - h5py._conv - DEBUG - Creating converter from 5 to 7
2025-07-01 02:49:36,722 - __main__ - INFO - Training models...
2025-07-01 02:49:36,722 - model_training.core - INFO - Starting train_model function
2025-07-01 02:49:36,722 - model_training.core - INFO - Shape of X: (760, 34)
2025-07-01 02:49:36,729 - model_training.core - INFO - 
Training model for three_way
2025-07-01 02:49:37,091 - model_training.core - INFO - Class weights after adjustment: {0: np.float64(2.0132231044886666), 1: np.float64(3.249379200014064), 2: np.float64(1.3464825772518079)}
2025-07-01 02:49:52,104 - model_training.core - INFO - Three_way Model Accuracy: 0.5000
2025-07-01 02:49:52,104 - model_training.core - INFO - 
Classification Report:
2025-07-01 02:49:52,115 - model_training.core - INFO -               precision    recall  f1-score   support

        Away       0.67      0.58      0.62        62
        Draw       0.24      0.59      0.34        27
        Home       0.77      0.38      0.51        63

    accuracy                           0.50       152
   macro avg       0.56      0.52      0.49       152
weighted avg       0.64      0.50      0.53       152

2025-07-01 02:49:52,303 - model_training.feature_analysis - ERROR - Error in neural network feature importance analysis: analyze_neural_network_importance.<locals>.scoring_fn() takes 2 positional arguments but 3 were given
2025-07-01 02:49:52,640 - model_training.core - INFO - 
Training model for over_under_1_5
2025-07-01 02:49:52,828 - model_training.core - ERROR - Error calculating prediction weights: index 0 is out of bounds for axis 0 with size 0
2025-07-01 02:49:56,036 - model_training.core - INFO - Over_under_1_5 Model Accuracy: 0.8289
2025-07-01 02:49:56,036 - model_training.core - INFO - 
Classification Report:
2025-07-01 02:49:56,048 - model_training.core - INFO -               precision    recall  f1-score   support

    Over 1.5       0.86      0.94      0.90       126
   Under 1.5       0.50      0.27      0.35        26

    accuracy                           0.83       152
   macro avg       0.68      0.61      0.63       152
weighted avg       0.80      0.83      0.81       152

2025-07-01 02:49:56,212 - model_training.feature_analysis - ERROR - Error in feature importance analysis: 'CalibratedClassifierCV' object has no attribute 'base_estimator'
2025-07-01 02:49:56,212 - model_training.core - INFO - 
Training model for over_under_2_5
2025-07-01 02:49:56,366 - model_training.core - ERROR - Error calculating prediction weights: index 0 is out of bounds for axis 0 with size 0
2025-07-01 02:49:59,255 - model_training.core - INFO - Over_under_2_5 Model Accuracy: 0.6842
2025-07-01 02:49:59,255 - model_training.core - INFO - 
Classification Report:
2025-07-01 02:49:59,266 - model_training.core - INFO -               precision    recall  f1-score   support

    Over 2.5       0.69      0.85      0.76        91
   Under 2.5       0.66      0.44      0.53        61

    accuracy                           0.68       152
   macro avg       0.68      0.64      0.65       152
weighted avg       0.68      0.68      0.67       152

2025-07-01 02:49:59,434 - model_training.feature_analysis - ERROR - Error in feature importance analysis: 'CalibratedClassifierCV' object has no attribute 'base_estimator'
2025-07-01 02:49:59,434 - model_training.core - INFO - 
Training model for over_under_3_5
2025-07-01 02:49:59,593 - model_training.core - ERROR - Error calculating prediction weights: index 0 is out of bounds for axis 0 with size 0
2025-07-01 02:50:02,558 - model_training.core - INFO - Over_under_3_5 Model Accuracy: 0.6908
2025-07-01 02:50:02,558 - model_training.core - INFO - 
Classification Report:
2025-07-01 02:50:02,572 - model_training.core - INFO -               precision    recall  f1-score   support

    Over 3.5       0.59      0.54      0.56        56
   Under 3.5       0.74      0.78      0.76        96

    accuracy                           0.69       152
   macro avg       0.67      0.66      0.66       152
weighted avg       0.69      0.69      0.69       152

2025-07-01 02:50:02,723 - model_training.feature_analysis - ERROR - Error in feature importance analysis: 'CalibratedClassifierCV' object has no attribute 'base_estimator'
2025-07-01 02:50:02,723 - model_training.core - INFO - 
Training model for btts
2025-07-01 02:50:05,629 - model_training.core - INFO - Btts Model Accuracy: 0.5855
2025-07-01 02:50:05,630 - model_training.core - INFO - 
Classification Report:
2025-07-01 02:50:05,639 - model_training.core - INFO -               precision    recall  f1-score   support

          No       0.49      0.57      0.53        61
         Yes       0.68      0.59      0.63        91

    accuracy                           0.59       152
   macro avg       0.58      0.58      0.58       152
weighted avg       0.60      0.59      0.59       152

2025-07-01 02:50:05,782 - model_training.feature_analysis - ERROR - Error in feature importance analysis: 'CalibratedClassifierCV' object has no attribute 'base_estimator'
2025-07-01 02:50:05,783 - __main__ - INFO - ✅ Models trained successfully:
2025-07-01 02:50:05,783 - __main__ - INFO -    - three_way: Sequential
2025-07-01 02:50:05,783 - __main__ - INFO -      Features: 31
2025-07-01 02:50:05,783 - __main__ - DEBUG -      Feature names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength']...
2025-07-01 02:50:05,783 - __main__ - INFO -    - over_under_1_5: CalibratedClassifierCV
2025-07-01 02:50:05,783 - __main__ - INFO -      Features: 31
2025-07-01 02:50:05,783 - __main__ - DEBUG -      Feature names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength']...
2025-07-01 02:50:05,783 - __main__ - INFO -    - over_under_2_5: CalibratedClassifierCV
2025-07-01 02:50:05,783 - __main__ - INFO -      Features: 31
2025-07-01 02:50:05,783 - __main__ - DEBUG -      Feature names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength']...
2025-07-01 02:50:05,783 - __main__ - INFO -    - over_under_3_5: CalibratedClassifierCV
2025-07-01 02:50:05,783 - __main__ - INFO -      Features: 31
2025-07-01 02:50:05,783 - __main__ - DEBUG -      Feature names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength']...
2025-07-01 02:50:05,783 - __main__ - INFO -    - btts: CalibratedClassifierCV
2025-07-01 02:50:05,783 - __main__ - INFO -      Features: 31
2025-07-01 02:50:05,784 - __main__ - DEBUG -      Feature names: ['home_points_per_game', 'away_points_per_game', 'home_goals_scored_ratio', 'away_goals_scored_ratio', 'home_goals_conceded_ratio', 'away_goals_conceded_ratio', 'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 'away_defense_strength']...
2025-07-01 02:50:05,784 - __main__ - INFO - 
==================================================
2025-07-01 02:50:05,784 - __main__ - INFO - ⚽ STEP 4: Testing Single Match Prediction
2025-07-01 02:50:05,784 - __main__ - INFO - ==================================================
2025-07-01 02:50:05,878 - __main__ - INFO - Available teams: ['Arsenal', 'Aston Villa', 'Bournemouth', 'Brentford', 'Brighton', 'Chelsea', 'Crystal Palace', 'Everton', 'Fulham', 'Ipswich Town', 'Leicester City', 'Liverpool', 'Manchester City', 'Manchester Utd', 'Newcastle Utd', 'Nottm Forest', 'Southampton', 'Tottenham', 'West Ham Utd', 'Wolverhampton']
2025-07-01 02:50:05,878 - __main__ - INFO - Testing prediction for: Liverpool vs Southampton
2025-07-01 02:50:05,878 - prediction.core - DEBUG - Starting predict_match for Liverpool vs Southampton
2025-07-01 02:50:05,882 - prediction.expected_goals - DEBUG - Starting expected goals calculation
2025-07-01 02:50:05,882 - prediction.expected_goals - DEBUG - Scoring rates - Home: 0.88, Away: 0.27
2025-07-01 02:50:05,882 - prediction.expected_goals - DEBUG - Conceding rates - Home: 0.34, Away: 0.82
2025-07-01 02:50:05,882 - prediction.expected_goals - DEBUG - League averages - Home: 1.51, Away: 1.42
2025-07-01 02:50:05,882 - prediction.expected_goals - DEBUG - Attack strengths - Home: 1.73, Away: 0.80
2025-07-01 02:50:05,882 - prediction.expected_goals - DEBUG - Defense strengths - Home: 5.95, Away: 2.44
2025-07-01 02:50:05,882 - prediction.expected_goals - DEBUG - Form factors - Home: 0.74, Away: 0.11
2025-07-01 02:50:05,882 - prediction.expected_goals - DEBUG - Base xG - Home: 1.35, Away: 0.61
2025-07-01 02:50:05,882 - prediction.expected_goals - INFO - Team strengths - Home: 0.81, Away: 0.11
2025-07-01 02:50:05,882 - prediction.expected_goals - DEBUG - After home advantage adjustment - Home xG: 1.62, Away xG: 0.51
2025-07-01 02:50:05,883 - prediction.expected_goals - INFO - Final expected goals - Home: 1.62, Away: 0.51
2025-07-01 02:50:05,883 - prediction.core - INFO - Expected goals - Home: 1.62, Away: 0.51
2025-07-01 02:50:05,884 - prediction.core - INFO - 
Feature values for Liverpool vs Southampton:
2025-07-01 02:50:05,884 - prediction.core - INFO - home_points_per_game: 0.736842105263158
2025-07-01 02:50:05,884 - prediction.core - INFO - away_points_per_game: 0.10526315789473684
2025-07-01 02:50:05,884 - prediction.core - INFO - home_goals_scored_ratio: 0.4878587196467991
2025-07-01 02:50:05,884 - prediction.core - INFO - away_goals_scored_ratio: 0.15962441314553993
2025-07-01 02:50:05,884 - prediction.core - INFO - home_goals_conceded_ratio: 0.18543046357615892
2025-07-01 02:50:05,885 - prediction.core - INFO - away_goals_conceded_ratio: 0.48122065727699526
2025-07-01 02:50:05,885 - prediction.core - INFO - home_attack_strength: 1.7298542955855727
2025-07-01 02:50:05,885 - prediction.core - INFO - away_attack_strength: 0.803623217967759
2025-07-01 02:50:05,885 - prediction.core - INFO - home_defense_strength: 1.8116294285600556
2025-07-01 02:50:05,885 - prediction.core - INFO - away_defense_strength: 1.3286770533538377
2025-07-01 02:50:05,885 - prediction.core - INFO - home_form: 0.8149319842672149
2025-07-01 02:50:05,885 - prediction.core - INFO - away_form: 0.09140191503252103
2025-07-01 02:50:05,885 - prediction.core - INFO - home_recent_scoring_rate: 2.26
2025-07-01 02:50:05,885 - prediction.core - INFO - away_recent_scoring_rate: 0.68
2025-07-01 02:50:05,885 - prediction.core - INFO - home_recent_conceding_rate: 1.08
2025-07-01 02:50:05,885 - prediction.core - INFO - away_recent_conceding_rate: 2.26
2025-07-01 02:50:05,885 - prediction.core - INFO - home_team_position: 0.5
2025-07-01 02:50:05,886 - prediction.core - INFO - away_team_position: 0.5
2025-07-01 02:50:05,886 - prediction.core - INFO - position_difference: 0
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_home_recent_win_rate: 0.41
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_away_recent_win_rate: 0.35
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_recent_draw_rate: 0.24
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_team_a_win_percentage: 0.41
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_team_b_win_percentage: 0.35
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_draw_percentage: 0.24
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_total_matches: 0
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_team_a_goals: 1.51
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_team_b_goals: 1.42
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_btts_percentage: 0.57
2025-07-01 02:50:05,886 - prediction.core - INFO - h2h_goal_pattern: 0
2025-07-01 02:50:05,887 - prediction.core - INFO - Team: Liverpool
2025-07-01 02:50:05,887 - prediction.core - INFO - Home Team: Liverpool
2025-07-01 02:50:05,887 - prediction.core - INFO - Away Team: Southampton
2025-07-01 02:50:05,887 - prediction.core - INFO - expected_goals_home: 1.620477535301669
2025-07-01 02:50:05,887 - prediction.core - INFO - expected_goals_away: 0.5142606516290725
2025-07-01 02:50:05,887 - prediction.core - INFO - expected_goals_total: 2.1347381869307416
2025-07-01 02:50:05,887 - prediction.core - INFO - form_data_valid: 1
2025-07-01 02:50:05,890 - prediction.core - DEBUG - Scaling features for three_way prediction
2025-07-01 02:50:05,974 - prediction.core - WARNING - No label encoder provided for three_way prediction, using fallback mapping
2025-07-01 02:50:05,975 - prediction.core - DEBUG - Scaling features for over_under_1_5 prediction
2025-07-01 02:50:06,068 - prediction.core - DEBUG - Scaling features for over_under_2_5 prediction
2025-07-01 02:50:06,155 - prediction.core - DEBUG - Scaling features for over_under_3_5 prediction
2025-07-01 02:50:06,241 - prediction.core - DEBUG - Scaling features for btts prediction
2025-07-01 02:50:06,333 - prediction.probabilities - DEBUG - Calculating double chance probabilities from: {'Home': np.float64(0.2916194215658034), 'Draw': np.float64(0.23618513345718378), 'Away': np.float64(0.47219544497701277)}
2025-07-01 02:50:06,333 - prediction.probabilities - DEBUG - Double chance probabilities: {'Home or Draw': np.float64(0.26573784393617056), 'Away or Draw': np.float64(0.3566538519000888), 'Home or Away': np.float64(0.3776083041637406)}
2025-07-01 02:50:06,333 - prediction.scores - DEBUG - Starting score prediction with expected goals: (np.float64(1.620477535301669), np.float64(0.5142606516290725))
2025-07-01 02:50:06,333 - prediction.scores - DEBUG - Starting Dixon-Coles probability calculation
2025-07-01 02:50:06,333 - prediction.scores - DEBUG - Dixon-Coles expected goals - Home: 1.98, Away: 0.47
2025-07-01 02:50:06,353 - prediction.scores - DEBUG - Dixon-Coles probability calculation completed
2025-07-01 02:50:06,374 - prediction.scores - INFO - Score prediction completed
2025-07-01 02:50:06,374 - __main__ - INFO - ✅ Prediction completed successfully!
2025-07-01 02:50:06,374 - __main__ - INFO - 📊 PREDICTION RESULTS:
2025-07-01 02:50:06,374 - __main__ - INFO - Match: Liverpool vs Southampton
2025-07-01 02:50:06,374 - __main__ - INFO - 
THREE_WAY:
2025-07-01 02:50:06,374 - __main__ - INFO -    Prediction: Away
2025-07-01 02:50:06,375 - __main__ - INFO -    Probabilities:
2025-07-01 02:50:06,375 - __main__ - INFO -      Home: 0.292
2025-07-01 02:50:06,375 - __main__ - INFO -      Draw: 0.236
2025-07-01 02:50:06,375 - __main__ - INFO -      Away: 0.472
2025-07-01 02:50:06,375 - __main__ - INFO - 
OVER_UNDER_1_5:
2025-07-01 02:50:06,375 - __main__ - INFO -    Prediction: Over 1.5
2025-07-01 02:50:06,375 - __main__ - INFO -    Probabilities:
2025-07-01 02:50:06,375 - __main__ - INFO -      Under 1.5: 0.383
2025-07-01 02:50:06,375 - __main__ - INFO -      Over 1.5: 0.617
2025-07-01 02:50:06,375 - __main__ - INFO - 
OVER_UNDER_2_5:
2025-07-01 02:50:06,375 - __main__ - INFO -    Prediction: Over 2.5
2025-07-01 02:50:06,375 - __main__ - INFO -    Probabilities:
2025-07-01 02:50:06,375 - __main__ - INFO -      Under 2.5: 0.482
2025-07-01 02:50:06,375 - __main__ - INFO -      Over 2.5: 0.518
2025-07-01 02:50:06,375 - __main__ - INFO - 
OVER_UNDER_3_5:
2025-07-01 02:50:06,375 - __main__ - INFO -    Prediction: Under 3.5
2025-07-01 02:50:06,375 - __main__ - INFO -    Probabilities:
2025-07-01 02:50:06,376 - __main__ - INFO -      Under 3.5: 0.665
2025-07-01 02:50:06,376 - __main__ - INFO -      Over 3.5: 0.335
2025-07-01 02:50:06,376 - __main__ - INFO - 
BTTS:
2025-07-01 02:50:06,376 - __main__ - INFO -    Prediction: Yes
2025-07-01 02:50:06,376 - __main__ - INFO -    Probabilities:
2025-07-01 02:50:06,376 - __main__ - INFO -      No: 0.429
2025-07-01 02:50:06,376 - __main__ - INFO -      Yes: 0.571
2025-07-01 02:50:06,376 - __main__ - INFO - 
DOUBLE_CHANCE:
2025-07-01 02:50:06,376 - __main__ - INFO -    Prediction: Home or Away
2025-07-01 02:50:06,376 - __main__ - INFO -    Probabilities:
2025-07-01 02:50:06,376 - __main__ - INFO -      Home or Draw: 0.266
2025-07-01 02:50:06,376 - __main__ - INFO -      Away or Draw: 0.357
2025-07-01 02:50:06,376 - __main__ - INFO -      Home or Away: 0.378
2025-07-01 02:50:06,376 - __main__ - INFO - 
Expected Goals:
2025-07-01 02:50:06,376 - __main__ - INFO -    Home: 1.62
2025-07-01 02:50:06,376 - __main__ - INFO -    Away: 0.51
2025-07-01 02:50:06,376 - __main__ - INFO - 
Top Correct Scores:
2025-07-01 02:50:06,376 - __main__ - INFO -    1-0: 0.195
2025-07-01 02:50:06,376 - __main__ - INFO -    2-0: 0.183
2025-07-01 02:50:06,376 - __main__ - INFO -    3-0: 0.110
2025-07-01 02:50:06,377 - __main__ - INFO -    2-1: 0.088
2025-07-01 02:50:06,377 - __main__ - INFO -    0-0: 0.087
2025-07-01 02:50:06,377 - __main__ - INFO - 
==================================================
2025-07-01 02:50:06,377 - __main__ - INFO - 📈 STEP 5: Testing Analysis and Output
2025-07-01 02:50:06,377 - __main__ - INFO - ==================================================
2025-07-01 02:50:06,377 - __main__ - INFO - Analyzing prediction confidence...
2025-07-01 02:50:06,377 - prediction.analysis - DEBUG - Starting prediction confidence analysis
2025-07-01 02:50:06,377 - prediction.analysis - DEBUG - Maximum probability for three_way: 0.472
2025-07-01 02:50:06,377 - prediction.analysis - DEBUG - Maximum probability for over_under_1_5: 0.617
2025-07-01 02:50:06,377 - prediction.analysis - DEBUG - Maximum probability for over_under_2_5: 0.518
2025-07-01 02:50:06,377 - prediction.analysis - DEBUG - Maximum probability for over_under_3_5: 0.665
2025-07-01 02:50:06,377 - prediction.analysis - DEBUG - Maximum probability for btts: 0.571
2025-07-01 02:50:06,377 - prediction.analysis - DEBUG - Maximum probability for double_chance: 0.378
2025-07-01 02:50:06,377 - __main__ - INFO - ✅ Confidence analysis completed
2025-07-01 02:50:06,377 - __main__ - DEBUG - Confidence analysis: {'three_way': {'confidence_level': 'Low', 'max_probability': np.float64(0.47219544497701277), 'probability_difference': np.float64(0.18057602341120937), 'prediction_strength': 'Weak'}, 'over_under_1_5': {'confidence_level': 'Medium', 'max_probability': np.float64(0.6168509427088872), 'probability_difference': np.float64(0.2337018854177743), 'prediction_strength': 'Weak'}, 'over_under_2_5': {'confidence_level': 'Low', 'max_probability': np.float64(0.518145991644217), 'probability_difference': np.float64(0.03629198328843397), 'prediction_strength': 'Weak'}, 'over_under_3_5': {'confidence_level': 'Medium', 'max_probability': np.float64(0.6649468871782378), 'probability_difference': np.float64(0.3298937743564756), 'prediction_strength': 'Weak'}, 'btts': {'confidence_level': 'Medium', 'max_probability': 0.5707283470863255, 'probability_difference': 0.14145669417265094, 'prediction_strength': 'Weak'}, 'double_chance': {'confidence_level': 'Low', 'max_probability': np.float64(0.3776083041637406), 'probability_difference': np.float64(0.11187046022757002), 'prediction_strength': 'Weak'}}
2025-07-01 02:50:06,377 - __main__ - INFO - Assessing prediction risk...
2025-07-01 02:50:06,377 - prediction.analysis - DEBUG - Starting prediction risk assessment
2025-07-01 02:50:06,377 - __main__ - INFO - ✅ Risk assessment completed
2025-07-01 02:50:06,378 - __main__ - DEBUG - Risk assessment: {'three_way': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.47219544497701277), 'probability_difference': np.float64(0.18057602341120937), 'prediction_balance': 'Clear Favorite'}}, 'over_under_1_5': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': np.float64(0.6168509427088872), 'probability_distribution': 'Skewed'}}, 'over_under_2_5': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.518145991644217), 'probability_distribution': 'Balanced'}}, 'over_under_3_5': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': np.float64(0.6649468871782378), 'probability_distribution': 'Skewed'}}, 'btts': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': 0.5707283470863255, 'probability_distribution': 'Balanced'}}, 'double_chance': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.3776083041637406), 'probability_difference': np.float64(0.11187046022757002)}}, 'overall': {'risk_level': 'High', 'risk_score': 2.75}}
2025-07-01 02:50:06,378 - __main__ - INFO - Generating prediction summary...
2025-07-01 02:50:06,378 - prediction.analysis - DEBUG - Generating prediction summary
2025-07-01 02:50:06,378 - prediction.analysis - DEBUG - Prediction summary generated successfully
2025-07-01 02:50:06,378 - __main__ - INFO - ✅ Prediction summary generated
2025-07-01 02:50:06,378 - __main__ - INFO - Saving predictions to Excel...
2025-07-01 02:50:06,378 - prediction.excel_output - INFO - Starting Excel output process
2025-07-01 02:50:06,378 - prediction.excel_output - INFO - Processing predictions for ENGLAND_PREMIER_LEAGUE_TEST:
2025-07-01 02:50:06,378 - prediction.excel_output - INFO - 
Match: Test Match
2025-07-01 02:50:06,378 - prediction.excel_output - INFO - Prediction data: {'main_predictions': {'three_way': {'prediction': 'Away', 'probabilities': {'Home': np.float64(0.2916194215658034), 'Draw': np.float64(0.23618513345718378), 'Away': np.float64(0.47219544497701277)}}, 'over_under_1_5': {'prediction': 'Over 1.5', 'probabilities': {'Under 1.5': np.float64(0.38314905729111287), 'Over 1.5': np.float64(0.6168509427088872)}}, 'over_under_2_5': {'prediction': 'Over 2.5', 'probabilities': {'Under 2.5': np.float64(0.48185400835578307), 'Over 2.5': np.float64(0.518145991644217)}}, 'over_under_3_5': {'prediction': 'Under 3.5', 'probabilities': {'Under 3.5': np.float64(0.6649468871782378), 'Over 3.5': np.float64(0.3350531128217622)}}, 'btts': {'prediction': 'Yes', 'probabilities': {'No': 0.42927165291367453, 'Yes': 0.5707283470863255}}, 'double_chance': {'prediction': 'Home or Away', 'probabilities': {'Home or Draw': np.float64(0.26573784393617056), 'Away or Draw': np.float64(0.3566538519000888), 'Home or Away': np.float64(0.3776083041637406)}}}, 'confidence_levels': {'three_way': {'confidence_level': 'Low', 'max_probability': np.float64(0.47219544497701277), 'probability_difference': np.float64(0.18057602341120937), 'prediction_strength': 'Weak'}, 'over_under_1_5': {'confidence_level': 'Medium', 'max_probability': np.float64(0.6168509427088872), 'probability_difference': np.float64(0.2337018854177743), 'prediction_strength': 'Weak'}, 'over_under_2_5': {'confidence_level': 'Low', 'max_probability': np.float64(0.518145991644217), 'probability_difference': np.float64(0.03629198328843397), 'prediction_strength': 'Weak'}, 'over_under_3_5': {'confidence_level': 'Medium', 'max_probability': np.float64(0.6649468871782378), 'probability_difference': np.float64(0.3298937743564756), 'prediction_strength': 'Weak'}, 'btts': {'confidence_level': 'Medium', 'max_probability': 0.5707283470863255, 'probability_difference': 0.14145669417265094, 'prediction_strength': 'Weak'}, 'double_chance': {'confidence_level': 'Low', 'max_probability': np.float64(0.3776083041637406), 'probability_difference': np.float64(0.11187046022757002), 'prediction_strength': 'Weak'}}, 'score_predictions': {'most_likely_scores': {'1-0': np.float64(0.19454142462908364), '2-0': np.float64(0.18285359360556752), '3-0': np.float64(0.11008114243253564), '2-1': np.float64(0.08826711235289651), '0-0': np.float64(0.08727586904686088)}, 'expected_total_goals': np.float64(2.3681270960153284)}, 'value_analysis': {}, 'risk_assessment': {'three_way': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.47219544497701277), 'probability_difference': np.float64(0.18057602341120937), 'prediction_balance': 'Clear Favorite'}}, 'over_under_1_5': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': np.float64(0.6168509427088872), 'probability_distribution': 'Skewed'}}, 'over_under_2_5': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.518145991644217), 'probability_distribution': 'Balanced'}}, 'over_under_3_5': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': np.float64(0.6649468871782378), 'probability_distribution': 'Skewed'}}, 'btts': {'risk_level': 'Medium', 'factors': {'prediction_confidence': 'Medium', 'probability_margin': 0.5707283470863255, 'probability_distribution': 'Balanced'}}, 'double_chance': {'risk_level': 'High', 'factors': {'prediction_confidence': 'Low', 'probability_margin': np.float64(0.3776083041637406), 'probability_difference': np.float64(0.11187046022757002)}}, 'overall': {'risk_level': 'High', 'risk_score': 2.75}}}
2025-07-01 02:50:06,381 - prediction.excel_output - ERROR - Error processing match Test Match: not enough values to unpack (expected 2, got 1)
2025-07-01 02:50:06,398 - prediction.excel_output - INFO - Predictions saved to /home/<USER>/projects/betting_project/data/processed/ENGLAND_PREMIER_LEAGUE_TEST_match_predictions.xlsx
2025-07-01 02:50:06,398 - __main__ - INFO - ✅ Predictions saved to Excel file
2025-07-01 02:50:06,399 - __main__ - INFO - 
================================================================================
2025-07-01 02:50:06,399 - __main__ - INFO - 🎉 PREDICTION PIPELINE TEST COMPLETED SUCCESSFULLY!
2025-07-01 02:50:06,399 - __main__ - INFO - ================================================================================
2025-07-01 02:50:06,399 - __main__ - INFO - ✅ All pipeline components tested successfully:
2025-07-01 02:50:06,399 - __main__ - INFO -    ✓ Data loading
2025-07-01 02:50:06,399 - __main__ - INFO -    ✓ Feature preparation
2025-07-01 02:50:06,399 - __main__ - INFO -    ✓ Model training
2025-07-01 02:50:06,399 - __main__ - INFO -    ✓ Match prediction
2025-07-01 02:50:06,399 - __main__ - INFO -    ✓ Analysis and output
2025-07-01 02:50:06,399 - __main__ - INFO - 
📄 Detailed logs saved to: logs/prediction_pipeline_test_20250701_024928.log
2025-07-01 02:50:06,399 - __main__ - INFO - 📊 Excel output saved to: data/processed/
2025-07-01 02:50:06,399 - __main__ - INFO - 🕒 Test completed at: 2025-07-01 02:50:06.399557
