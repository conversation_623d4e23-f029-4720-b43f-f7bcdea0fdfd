"""
Constants used throughout the model training package.
"""

# Neural Network Constants - Optimized for three-way classification
DENSE_LAYERS = [256, 128, 64, 32]  # Simpler architecture to prevent overfitting
DROPOUT_RATE = 0.3  # Reduced dropout to allow more learning
LEARNING_RATE = 0.001  # Higher learning rate for better convergence
BATCH_SIZE = 16  # Smaller batch size for better gradient updates
EPOCHS = 100  # More epochs for better learning
PATIENCE = 15  # Reduced patience for faster convergence detection
MIN_LR = 1e-6  # Higher minimum learning rate

# Random Forest Constants
RF_N_ESTIMATORS = 200
RF_MAX_DEPTH = 8
RF_MIN_SAMPLES_SPLIT = 15
RF_MIN_SAMPLES_LEAF = 8

# SMOTE Constants - Enhanced for better class balancing
MIN_SAMPLES_FOR_SMOTE = 8  # Reduced threshold to apply SMOTE more often
SMOTE_K_NEIGHBORS = 3  # Reduced k_neighbors for small minority classes

# Cross-validation Constants
N_SPLITS = 5
TRAIN_SIZE = 0.8

# Class Weight Constants - More aggressive balancing
DRAW_WEIGHT_MULTIPLIER = 4.0  # Increased draw weight
HOME_AWAY_WEIGHT_MULTIPLIER = 2.0  # Balanced home/away weights
CLASS_WEIGHT_POWER = 1.2  # Reduced power for smoother weighting

# SMOTE Constants
SMOTE_K_NEIGHBORS = 5  # Number of neighbors for SMOTE

# String Column Constants
STRING_COLUMNS = [
    "Team",
    "Home Team",
    "Away Team",
    "correct_score",
    "form_data_valid_str",
    "btts"
]
