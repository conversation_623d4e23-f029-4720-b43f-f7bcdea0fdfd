"""
Core feature engineering functionality.
Handles the main feature engineering process and coordinates all components.
"""

import logging
import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder
from typing import Dict, Optional, Union, List, Tuple, Any

from .constants import MIN_MATCHES_VALID, STRING_COLUMNS
from .strength import (
    calculate_team_strength,
    calculate_attack_strength,
    calculate_defense_strength,
    calculate_form
)
from .h2h import calculate_h2h_strength
from .utils import (
    validate_team_stats,
    check_form_data_validity,
    get_match_outcome_features,
    normalize_features,
    get_numeric_columns
)

logger = logging.getLogger(__name__)

def engineer_features(
    home_team: str,
    away_team: str,
    home_stats: pd.Series,
    away_stats: pd.Series,
    h2h_data: Optional[pd.Series],
    league_stats_dict: Dict[str, float],
    league_table: pd.DataFrame,
    team_stats_mapping: Dict[str, str],
    h2h_stats_mapping: Dict[str, str],
    label_encoders: Optional[Dict[str, LabelEncoder]] = None,
) -> Optional[Dict[str, Union[str, float]]]:
    """
    Engineer features for a single match with enhanced calculations.

    Args:
        home_team: Name of the home team
        away_team: Name of the away team
        home_stats: Statistics for the home team
        away_stats: Statistics for the away team
        h2h_data: Head-to-head statistics for the two teams
        league_stats_dict: Dictionary of league-wide statistics
        league_table: Current league standings
        team_stats_mapping: Mapping of team statistic names
        h2h_stats_mapping: Mapping of head-to-head statistic names
        label_encoders: Dictionary of label encoders for categorical variables

    Returns:
        Dictionary of engineered features for the match
    """
    try:
        features = {}

        # Basic team stats
        features.update(_get_basic_team_stats(home_stats, away_stats, league_stats_dict))

        # Enhanced strength calculations
        features.update(_calculate_strength_features(
            home_stats, away_stats, league_stats_dict
        ))

        # League position features
        features.update(_get_league_position_features(
            home_team, away_team, league_table
        ))

        # Head-to-head features
        features.update(_get_h2h_features(
            h2h_data, home_team, away_team, league_stats_dict
        ))

        # Advanced feature interactions
        features.update(_calculate_advanced_features(
            home_stats, away_stats, league_stats_dict, league_table, home_team, away_team
        ))

        # Momentum and trend features
        features.update(_calculate_momentum_features(
            home_stats, away_stats, league_stats_dict
        ))

        # Team identifiers
        features.update({
            "Team": home_team,
            "Home Team": home_team,
            "Away Team": away_team
        })

        # Normalize features
        features = normalize_features(features)

        return features

    except Exception as e:
        logger.error(f"Error engineering features: {str(e)}")
        return None


def _calculate_advanced_features(
    home_stats: pd.Series,
    away_stats: pd.Series,
    league_stats_dict: Dict[str, float],
    league_table: pd.DataFrame,
    home_team: str,
    away_team: str
) -> Dict[str, float]:
    """Calculate advanced feature interactions and derived metrics."""
    features = {}

    try:
        # Strength differential features
        home_attack = home_stats["goals_scored_per_match_home"]
        away_defense = away_stats["goals_conceded_per_match_away"]
        away_attack = away_stats["goals_scored_per_match_away"]
        home_defense = home_stats["goals_conceded_per_match_home"]

        # Attack vs Defense matchups
        features["home_attack_vs_away_defense"] = home_attack / (away_defense + 0.1)
        features["away_attack_vs_home_defense"] = away_attack / (home_defense + 0.1)
        features["attack_defense_ratio"] = features["home_attack_vs_away_defense"] / (features["away_attack_vs_home_defense"] + 0.1)

        # Form differential
        home_form = home_stats.get("ppg_last_8", 0)
        away_form = away_stats.get("ppg_last_8", 0)
        features["form_differential"] = home_form - away_form
        features["form_ratio"] = home_form / (away_form + 0.1)

        # League position features
        try:
            home_pos = league_table[league_table["Team"] == home_team]["Position"].iloc[0]
            away_pos = league_table[league_table["Team"] == away_team]["Position"].iloc[0]
            features["position_advantage"] = away_pos - home_pos  # Positive if home team is higher
            features["position_ratio"] = away_pos / (home_pos + 0.1)
        except:
            features["position_advantage"] = 0
            features["position_ratio"] = 1.0

        # Goal scoring consistency
        home_goals_variance = _calculate_goal_variance(home_stats, True)
        away_goals_variance = _calculate_goal_variance(away_stats, False)
        features["home_goal_consistency"] = 1 / (1 + home_goals_variance)
        features["away_goal_consistency"] = 1 / (1 + away_goals_variance)

        # Efficiency metrics
        home_efficiency = home_stats["total_wins"] / (home_stats["total_played"] + 0.1)
        away_efficiency = away_stats["total_wins"] / (away_stats["total_played"] + 0.1)
        features["efficiency_differential"] = home_efficiency - away_efficiency

        return features

    except Exception as e:
        logger.warning(f"Error calculating advanced features: {e}")
        return {}


def _calculate_momentum_features(
    home_stats: pd.Series,
    away_stats: pd.Series,
    league_stats_dict: Dict[str, float]
) -> Dict[str, float]:
    """Calculate momentum and trend-based features."""
    features = {}

    try:
        # Recent form momentum
        home_recent_goals = home_stats.get("avg_goals_scored_last_8", 0)
        away_recent_goals = away_stats.get("avg_goals_scored_last_8", 0)
        home_recent_conceded = home_stats.get("avg_goals_conceded_last_8", 0)
        away_recent_conceded = away_stats.get("avg_goals_conceded_last_8", 0)

        # Goal momentum
        features["home_goal_momentum"] = home_recent_goals - home_stats.get("goals_scored_per_match_all", 0)
        features["away_goal_momentum"] = away_recent_goals - away_stats.get("goals_scored_per_match_all", 0)
        features["home_defense_momentum"] = home_stats.get("goals_conceded_per_match_all", 0) - home_recent_conceded
        features["away_defense_momentum"] = away_stats.get("goals_conceded_per_match_all", 0) - away_recent_conceded

        # Combined momentum score
        features["home_total_momentum"] = features["home_goal_momentum"] + features["home_defense_momentum"]
        features["away_total_momentum"] = features["away_goal_momentum"] + features["away_defense_momentum"]
        features["momentum_differential"] = features["home_total_momentum"] - features["away_total_momentum"]

        # Venue-specific momentum
        home_venue_form = home_stats.get("home_points_per_game", 0)
        away_venue_form = away_stats.get("away_points_per_game", 0)
        league_avg_home = league_stats_dict.get("home_points_per_game", 1.5)
        league_avg_away = league_stats_dict.get("away_points_per_game", 1.0)

        features["home_venue_momentum"] = home_venue_form - league_avg_home
        features["away_venue_momentum"] = away_venue_form - league_avg_away
        features["venue_momentum_differential"] = features["home_venue_momentum"] - features["away_venue_momentum"]

        return features

    except Exception as e:
        logger.warning(f"Error calculating momentum features: {e}")
        return {}


def _calculate_goal_variance(team_stats: pd.Series, is_home: bool) -> float:
    """Calculate goal scoring variance as a measure of consistency."""
    try:
        if is_home:
            avg_goals = team_stats.get("goals_scored_per_match_home", 0)
            total_goals = team_stats.get("goals_scored_home", 0)
            matches = team_stats.get("total_home_played", 1)
        else:
            avg_goals = team_stats.get("goals_scored_per_match_away", 0)
            total_goals = team_stats.get("goals_scored_away", 0)
            matches = team_stats.get("total_away_played", 1)

        # Simple variance approximation
        if matches > 1:
            return abs(total_goals - avg_goals * matches) / matches
        return 0.0

    except Exception:
        return 0.0

def prepare_features(
    results: pd.DataFrame,
    team_stats: pd.DataFrame,
    league_stats: pd.DataFrame,
    h2h_stats: pd.DataFrame,
    league_table: pd.DataFrame,
    column_mapping: Dict[str, str],
    team_mapping: Dict[str, str],
) -> Optional[pd.DataFrame]:
    """
    Prepare features for model training with enhanced feature engineering.

    Args:
        results: DataFrame containing match results
        team_stats: DataFrame containing team statistics
        league_stats: DataFrame containing league statistics
        h2h_stats: DataFrame containing head-to-head statistics
        league_table: DataFrame containing current league standings
        column_mapping: Dictionary mapping feature names to column names
        team_mapping: Dictionary mapping team names to standardized names

    Returns:
        DataFrame containing engineered features for all matches
    """
    try:
        logger.info(f"Starting feature preparation for {len(results)} matches")

        # Convert league stats to dict for easier access
        league_stats_dict = dict(zip(league_stats["Stat"], league_stats["Value"]))

        # Initialize feature dataframe
        features = []
        skipped = 0

        for _, match in results.iterrows():
            try:
                # Get and validate team stats
                home_stats, away_stats = validate_team_stats(
                    match["Home Team"],
                    match["Away Team"],
                    team_stats
                )

                if home_stats is None or away_stats is None:
                    skipped += 1
                    continue

                # Get H2H stats
                h2h_data = _get_h2h_data(
                    match["Home Team"],
                    match["Away Team"],
                    h2h_stats
                )

                # Engineer features
                match_features = engineer_features(
                    match["Home Team"],
                    match["Away Team"],
                    home_stats,
                    away_stats,
                    h2h_data,
                    league_stats_dict,
                    league_table,
                    column_mapping,
                    {},  # Empty dict for h2h mapping during training
                    None,  # No label encoders during training
                )

                if match_features:
                    # Add form data validity
                    validity_str, validity_flag = check_form_data_validity(
                        home_stats, away_stats, column_mapping
                    )
                    match_features["form_data_valid_str"] = validity_str
                    match_features["form_data_valid"] = validity_flag

                    # Add match outcomes
                    match_features.update(get_match_outcome_features(
                        match["Home Score"],
                        match["Away Score"]
                    ))

                    features.append(match_features)
                else:
                    skipped += 1

            except Exception as e:
                logger.error(
                    f"Error processing match {match['Home Team']} vs {match['Away Team']}: {str(e)}"
                )
                skipped += 1
                continue

        if not features:
            logger.error("No valid features generated")
            return None

        features_df = pd.DataFrame(features)
        logger.info(
            f"Processing {len(features_df)} valid matches. Skipped {skipped} matches."
        )

        # Create and fit label encoders
        features_df = _create_label_encoders(features_df)

        return features_df

    except Exception as e:
        logger.error(f"Error in feature preparation: {str(e)}")
        return None

def _get_basic_team_stats(
    home_stats: pd.Series,
    away_stats: pd.Series,
    league_stats_dict: Dict[str, float]
) -> Dict[str, float]:
    """Get basic team statistics."""
    features = {}

    # Points per game
    features["home_points_per_game"] = home_stats["points_per_game"]
    features["away_points_per_game"] = away_stats["points_per_game"]

    # Goal ratios
    features["home_goals_scored_ratio"] = (
        home_stats["goals_scored_per_match_home"] /
        float(league_stats_dict.get("home_goals_per_match"))
    )
    features["away_goals_scored_ratio"] = (
        away_stats["goals_scored_per_match_away"] /
        float(league_stats_dict.get("away_goals_per_match"))
    )
    features["home_goals_conceded_ratio"] = (
        home_stats["goals_conceded_per_match_home"] /
        float(league_stats_dict.get("home_goals_per_match"))
    )
    features["away_goals_conceded_ratio"] = (
        away_stats["goals_conceded_per_match_away"] /
        float(league_stats_dict.get("away_goals_per_match"))
    )

    return features

def _calculate_strength_features(
    home_stats: pd.Series,
    away_stats: pd.Series,
    league_stats_dict: Dict[str, float]
) -> Dict[str, float]:
    """Calculate team strength features."""
    features = {}

    # Calculate various strength metrics
    features["home_attack_strength"] = calculate_attack_strength(
        home_stats, league_stats_dict, is_home=True
    )
    features["away_attack_strength"] = calculate_attack_strength(
        away_stats, league_stats_dict, is_home=False
    )
    features["home_defense_strength"] = calculate_defense_strength(
        home_stats, league_stats_dict, is_home=True
    )
    features["away_defense_strength"] = calculate_defense_strength(
        away_stats, league_stats_dict, is_home=False
    )

    # Form calculations
    features["home_form"] = calculate_form(home_stats)
    features["away_form"] = calculate_form(away_stats)

    # Recent performance metrics
    features["home_recent_scoring_rate"] = home_stats["avg_goals_scored_last_8"]
    features["away_recent_scoring_rate"] = away_stats["avg_goals_scored_last_8"]
    features["home_recent_conceding_rate"] = home_stats["avg_goals_conceded_last_8"]
    features["away_recent_conceding_rate"] = away_stats["avg_goals_conceded_last_8"]

    return features

def _get_league_position_features(
    home_team: str,
    away_team: str,
    league_table: pd.DataFrame
) -> Dict[str, float]:
    """Get features related to league positions."""
    features = {}

    if league_table is not None:
        home_row = league_table[league_table["Team"] == home_team]
        away_row = league_table[league_table["Team"] == away_team]

        if not home_row.empty and not away_row.empty:
            total_teams = len(league_table)
            home_pos = home_row.iloc[0]["Position"]
            away_pos = away_row.iloc[0]["Position"]

            # Normalize positions to [0, 1] range
            features["home_team_position"] = (total_teams - home_pos + 1) / total_teams
            features["away_team_position"] = (total_teams - away_pos + 1) / total_teams
            features["position_difference"] = (
                features["home_team_position"] - features["away_team_position"]
            )
        else:
            features["home_team_position"] = 0.5
            features["away_team_position"] = 0.5
            features["position_difference"] = 0

    return features

def _get_h2h_features(
    h2h_data: Optional[pd.Series],
    home_team: str,
    away_team: str,
    league_stats_dict: Dict[str, float]
) -> Dict[str, float]:
    """Get head-to-head features."""
    features = {}

    h2h_strength = calculate_h2h_strength(
        h2h_data, home_team, away_team, league_stats_dict
    )

    if h2h_data is not None:
        features.update({
            "h2h_home_recent_win_rate": h2h_strength["home_advantage"],
            "h2h_away_recent_win_rate": h2h_strength["away_advantage"],
            "h2h_recent_draw_rate": h2h_strength["draw_tendency"],
            "h2h_team_a_win_percentage": h2h_data["team_a_win_percentage"] / 100,
            "h2h_team_b_win_percentage": h2h_data["team_b_win_percentage"] / 100,
            "h2h_draw_percentage": h2h_data["draw_percentage"] / 100,
            "h2h_total_matches": h2h_data["total_matches"],
            "h2h_team_a_goals": h2h_data["team_a_goals"],
            "h2h_team_b_goals": h2h_data["team_b_goals"],
            "h2h_btts_percentage": h2h_data["btts_percentage"] / 100,
            "h2h_goal_pattern": h2h_strength["goal_pattern"]
        })
    else:
        features.update({
            "h2h_home_recent_win_rate": float(league_stats_dict.get("home_win_percentage")) / 100,
            "h2h_away_recent_win_rate": float(league_stats_dict.get("away_win_percentage")) / 100,
            "h2h_recent_draw_rate": float(league_stats_dict.get("draw_percentage")) / 100,
            "h2h_team_a_win_percentage": float(league_stats_dict.get("home_win_percentage")) / 100,
            "h2h_team_b_win_percentage": float(league_stats_dict.get("away_win_percentage")) / 100,
            "h2h_draw_percentage": float(league_stats_dict.get("draw_percentage")) / 100,
            "h2h_total_matches": 0,
            "h2h_team_a_goals": float(league_stats_dict.get("home_goals_per_match")),
            "h2h_team_b_goals": float(league_stats_dict.get("away_goals_per_match")),
            "h2h_btts_percentage": float(league_stats_dict.get("both_teams_scored_percentage")) / 100,
            "h2h_goal_pattern": 0
        })

    return features

def _get_h2h_data(
    home_team: str,
    away_team: str,
    h2h_stats: pd.DataFrame
) -> Optional[pd.Series]:
    """Get head-to-head data for two teams."""
    h2h_row = h2h_stats[
        (h2h_stats["Matchup"] == f"{home_team} vs {away_team}") |
        (h2h_stats["Matchup"] == f"{away_team} vs {home_team}")
    ]
    return h2h_row.iloc[0] if not h2h_row.empty else None

def _create_label_encoders(features_df: pd.DataFrame) -> pd.DataFrame:
    """Create and fit label encoders for categorical variables."""
    label_encoders = {
        "three_way": LabelEncoder(),
        "over_under_1_5": LabelEncoder(),
        "over_under_2_5": LabelEncoder(),
        "over_under_3_5": LabelEncoder(),
        "btts": LabelEncoder(),
    }

    # Fit and transform all categorical labels
    for pred_type in label_encoders:
        unique_labels = features_df[pred_type].unique()
        logger.info(f"Unique labels for {pred_type}: {unique_labels}")
        label_encoders[pred_type].fit(unique_labels)
        features_df[f"{pred_type}_encoded"] = label_encoders[pred_type].transform(
            features_df[pred_type]
        )

    # Store the label encoders as an attribute of the dataframe
    features_df.attrs["label_encoders"] = label_encoders

    return features_df
