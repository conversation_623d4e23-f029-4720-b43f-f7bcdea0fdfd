"""
Constants used throughout the model training package.
"""

# Neural Network Constants - Enhanced for three-way classification
DENSE_LAYERS = [512, 256, 128, 64]  # Deeper and wider architecture
DROPOUT_RATE = 0.4  # Adjusted dropout rate
LEARNING_RATE = 0.0001  # Lower learning rate for fine-tuning
BATCH_SIZE = 32  # Increased batch size
EPOCHS = 25  
PATIENCE = 25  # Increased patience for better convergence
MIN_LR = 1e-7  # Lower minimum learning rate

# Random Forest Constants
RF_N_ESTIMATORS = 200
RF_MAX_DEPTH = 8
RF_MIN_SAMPLES_SPLIT = 15
RF_MIN_SAMPLES_LEAF = 8

# SMOTE Constants - Enhanced for better class balancing
MIN_SAMPLES_FOR_SMOTE = 8  # Reduced threshold to apply SMOTE more often
SMOTE_K_NEIGHBORS = 3  # Reduced k_neighbors for small minority classes

# Cross-validation Constants
N_SPLITS = 5
TRAIN_SIZE = 0.8

# Class Weight Constants
DRAW_WEIGHT_MULTIPLIER = 3.0
HOME_AWAY_WEIGHT_MULTIPLIER = 2.5
CLASS_WEIGHT_POWER = 1.5

# String Column Constants
STRING_COLUMNS = [
    "Team",
    "Home Team",
    "Away Team",
    "correct_score",
    "form_data_valid_str",
    "btts"
]
