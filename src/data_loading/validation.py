"""
Data validation and conversion functionality.
"""

import logging
import pandas as pd
from typing import Optional
from .constants import RESULTS_REQUIRED_COLUMNS, NON_NUMERIC_COLUMNS
from .utils import parse_date, check_required_columns, log_dataframe_info

logger = logging.getLogger(__name__)

def validate_and_convert_data(
    df: pd.DataFrame,
    data_type: str
) -> Optional[pd.DataFrame]:
    """
    Validate and convert data types for loaded DataFrames.

    Args:
        df: DataFrame to validate and convert
        data_type: Type of data being validated ('results', 'team_stats', etc.)

    Returns:
        Validated and converted DataFrame, or None if validation fails

    Example:
        >>> df = pd.DataFrame({
        ...     'Date': ['2023-01-01'],
        ...     'Home Team': ['Team A'],
        ...     'Score': ['2-1'],
        ...     'Away Team': ['Team B']
        ... })
        >>> validated_df = validate_and_convert_data(df, "results")
        >>> print(validated_df['Home Score'].iloc[0])
        2
    """
    try:
        logger.debug(f"Validating {data_type} dataframe with columns: {df.columns.tolist()}")

        if df.empty:
            logger.error(f"Empty dataframe provided for {data_type}")
            return None

        if data_type == "results":
            df = _validate_and_convert_results(df)
        else:
            df = _validate_and_convert_stats(df, data_type)

        if df is not None:
            log_dataframe_info(df, data_type)
            
        return df

    except Exception as e:
        logger.error(f"Error validating {data_type} data: {str(e)}")
        return None

def _validate_and_convert_results(df: pd.DataFrame) -> Optional[pd.DataFrame]:
    """
    Validate and convert results data.

    Args:
        df: Results DataFrame to validate and convert

    Returns:
        Validated and converted DataFrame, or None if validation fails
    """
    try:
        # Check required columns
        if not check_required_columns(df, RESULTS_REQUIRED_COLUMNS, "results"):
            return None

        # Parse dates
        df["Date"] = df["Date"].apply(parse_date)

        # Split and convert scores
        df[["Home Score", "Away Score"]] = df["Score"].str.split("-", expand=True)
        df["Home Score"] = pd.to_numeric(df["Home Score"], errors="coerce")
        df["Away Score"] = pd.to_numeric(df["Away Score"], errors="coerce")

        # Add Result column (0 = Home win, 1 = Draw, 2 = Away win)
        df["Result"] = 1  # Default to Draw
        df.loc[df["Home Score"] > df["Away Score"], "Result"] = 0  # Home win
        df.loc[df["Home Score"] < df["Away Score"], "Result"] = 2  # Away win

        return df

    except Exception as e:
        logger.error(f"Error validating results data: {str(e)}")
        return None

def _validate_and_convert_stats(
    df: pd.DataFrame,
    data_type: str
) -> Optional[pd.DataFrame]:
    """
    Validate and convert statistics data.

    Args:
        df: Statistics DataFrame to validate and convert
        data_type: Type of statistics data

    Returns:
        Validated and converted DataFrame, or None if validation fails
    """
    try:
        # Get non-numeric columns for this data type
        non_numeric = NON_NUMERIC_COLUMNS.get(data_type, [])

        # Convert numeric columns
        for col in df.columns:
            if col not in non_numeric:
                df[col] = pd.to_numeric(df[col], errors="coerce")

        # Specific validations for each data type
        if data_type == "team_stats":
            if "Team" not in df.columns:
                logger.error("Team stats missing 'Team' column")
                return None
        elif data_type == "league_stats":
            if "Stat" not in df.columns or "Value" not in df.columns:
                logger.error("League stats missing required columns")
                return None
        elif data_type == "h2h_stats":
            if "Matchup" not in df.columns:
                logger.error("H2H stats missing 'Matchup' column")
                return None
        elif data_type == "league_table":
            required_cols = ["Team", "Position"]
            if not all(col in df.columns for col in required_cols):
                logger.error("League table missing required columns")
                return None
            df["Position"] = pd.to_numeric(df["Position"], errors="coerce")

        return df

    except Exception as e:
        logger.error(f"Error validating {data_type} data: {str(e)}")
        return None

def validate_league_config(config: dict) -> bool:
    """
    Validate league configuration dictionary.

    Args:
        config: League configuration dictionary to validate

    Returns:
        True if configuration is valid, False otherwise
    """
    try:
        required_keys = ["TEAM_NAME_MAPPING"]
        missing_keys = [key for key in required_keys if key not in config]
        
        if missing_keys:
            logger.error(f"Missing required keys in league config: {missing_keys}")
            return False
            
        if not isinstance(config["TEAM_NAME_MAPPING"], dict):
            logger.error("TEAM_NAME_MAPPING must be a dictionary")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"Error validating league config: {str(e)}")
        return False
