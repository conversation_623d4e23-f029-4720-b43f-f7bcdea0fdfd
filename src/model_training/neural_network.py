"""
Neural network model creation and training functionality.
"""

import tensorflow as tf
import logging
from typing import Optional
from .constants import (
    DENSE_LAYERS,
    DROPOUT_RATE,
    LEARNING_RATE,
    BATCH_SIZE,
    EPOCHS,
    PATIENCE,
    MIN_LR
)

logger = logging.getLogger(__name__)

def create_three_way_model(input_shape: int, num_classes: int) -> tf.keras.Sequential:
    """
    Create an enhanced neural network model for three-way prediction with improved architecture.

    Args:
        input_shape: Number of input features
        num_classes: Number of output classes

    Returns:
        Compiled neural network model optimized for three-way classification
    """
    try:
        # Simplified but effective architecture for three-way classification
        model = tf.keras.Sequential([
            # Input layer with moderate regularization
            tf.keras.layers.Dense(
                DENSE_LAYERS[0],
                activation="relu",
                input_shape=(input_shape,),
                kernel_regularizer=tf.keras.regularizers.l2(0.0001),  # Reduced regularization
                kernel_initializer='he_normal'
            ),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(DROPOUT_RATE),

            # Second layer
            tf.keras.layers.Dense(
                DENSE_LAYERS[1],
                activation="relu",
                kernel_regularizer=tf.keras.regularizers.l2(0.0001),
                kernel_initializer='he_normal'
            ),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(DROPOUT_RATE * 0.7),

            # Third layer
            tf.keras.layers.Dense(
                DENSE_LAYERS[2],
                activation="relu",
                kernel_regularizer=tf.keras.regularizers.l2(0.0001),
                kernel_initializer='he_normal'
            ),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(DROPOUT_RATE * 0.5),

            # Fourth layer - smaller for final feature extraction
            tf.keras.layers.Dense(
                DENSE_LAYERS[3],
                activation="relu",
                kernel_regularizer=tf.keras.regularizers.l2(0.0001),
                kernel_initializer='he_normal'
            ),
            tf.keras.layers.Dropout(DROPOUT_RATE * 0.3),  # Less dropout before output

            # Output layer with softmax for three-way classification
            tf.keras.layers.Dense(
                num_classes,
                activation="softmax",
                kernel_initializer='glorot_uniform'
            ),
        ])

        # Optimized Adam optimizer for three-way classification
        optimizer = tf.keras.optimizers.Adam(
            learning_rate=LEARNING_RATE,
            beta_1=0.9,
            beta_2=0.999,
            epsilon=1e-8,
            clipnorm=1.0  # Gradient clipping to prevent exploding gradients
        )

        model.compile(
            optimizer=optimizer,
            loss="categorical_crossentropy",
            metrics=["accuracy", "precision", "recall", "f1_score"],
        )

        logger.info(f"Created enhanced three-way model with {model.count_params()} parameters")
        return model

    except Exception as e:
        logger.error(f"Error creating three-way model: {str(e)}")
        raise

def get_callbacks() -> list:
    """Get optimized callbacks for three-way classification training."""
    return [
        tf.keras.callbacks.EarlyStopping(
            monitor="val_loss",  # Monitor loss for more stable stopping
            patience=PATIENCE,
            restore_best_weights=True,
            verbose=1,
            mode='min',  # Minimize loss
            min_delta=0.001  # Minimum change to qualify as improvement
        ),
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor="val_loss",
            factor=0.5,  # Conservative learning rate reduction
            patience=5,  # Faster adaptation to plateaus
            min_lr=MIN_LR,
            verbose=1,
            min_delta=0.001
        ),
        tf.keras.callbacks.ModelCheckpoint(
            filepath='best_three_way_model.weights.h5',
            monitor='val_accuracy',
            save_best_only=True,
            save_weights_only=True,
            mode='max',
            verbose=0
        )
    ]
