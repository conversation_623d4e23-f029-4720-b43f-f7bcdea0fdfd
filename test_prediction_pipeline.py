#!/usr/bin/env python3
"""
Comprehensive Test Script for the Main Prediction Pipeline

This script tests the entire prediction pipeline with one match from England Premier League,
including data loading, feature engineering, model training, and prediction generation.
All logs are saved to a detailed log file for analysis.
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime
import traceback
import json

# Add src to path
sys.path.append('src')


class NumpyEncoder(json.JSONEncoder):
    """A custom JSON encoder for NumPy data types."""
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)


def setup_detailed_logging():
    """Setup comprehensive logging to both console and file."""
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)

    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'logs/prediction_pipeline_test_{timestamp}.log'

    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, mode='w'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    # Suppress noisy third-party library logs
    noisy_loggers = [
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.font_manager',
        'matplotlib.backends',
        'matplotlib.colorbar',
        'matplotlib.figure',
        'PIL',
        'urllib3',
        'requests',
        'tensorflow',
        'absl'
    ]

    for logger_name in noisy_loggers:
        logging.getLogger(logger_name).setLevel(logging.WARNING)

    logger = logging.getLogger(__name__)
    logger.info("="*80)
    logger.info("🧪 PREDICTION PIPELINE COMPREHENSIVE TEST")
    logger.info("="*80)
    logger.info(f"Log file: {log_filename}")
    logger.info(f"Test started at: {datetime.now()}")

    return logger, log_filename

def test_data_loading(logger):
    """Test data loading functionality."""
    logger.info("\n" + "="*50)
    logger.info("📊 STEP 1: Testing Data Loading")
    logger.info("="*50)
    
    try:
        from data_loading import load_data, get_available_leagues
        from scrapers.config import LEAGUE_CONFIGS
        
        # Check available leagues
        available_leagues = get_available_leagues()
        logger.info(f"Available leagues: {len(available_leagues)}")
        # logger.debug(f"League list: {available_leagues[:10]}...")  # Show first 10
        
        # Test with England Premier League
        league_name = "ENGLAND_PREMIER_LEAGUE"
        if league_name not in available_leagues:
            logger.error(f"❌ {league_name} not found in available leagues")
            return None, None
            
        if league_name not in LEAGUE_CONFIGS:
            logger.error(f"❌ {league_name} configuration not found")
            return None, None
            
        league_config = LEAGUE_CONFIGS[league_name]
        logger.info(f"✅ League configuration loaded for {league_name}")
        logger.debug(f"Config keys: {list(league_config.keys())}")
        
        # Load data
        logger.info("Loading data files...")
        data, updated_config = load_data(league_name, league_config)
        
        if data is None:
            logger.error("❌ Failed to load data")
            return None, None
            
        results, team_stats, league_stats, h2h_stats, league_table = data
        
        # Log data summary
        logger.info("✅ Data loaded successfully:")
        logger.info(f"   - Results: {len(results)} matches")
        logger.info(f"   - Team stats: {len(team_stats)} teams")
        logger.info(f"   - League stats: {len(league_stats)} statistics")
        logger.info(f"   - H2H stats: {len(h2h_stats)} matchups")
        logger.info(f"   - League table: {len(league_table)} teams")
        
        # Log sample data (commented out to reduce log noise)
        # logger.debug("Sample results data:")
        # logger.debug(f"\n{results.head().to_string()}")
        # logger.debug("Sample team stats data:")
        # logger.debug(f"\n{team_stats.head().to_string()}")
        
        return data, updated_config
        
    except Exception as e:
        logger.error(f"❌ Error in data loading: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None, None

def test_feature_preparation(logger, data, league_config):
    """Test feature preparation and engineering."""
    logger.info("\n" + "="*50)
    logger.info("🔧 STEP 2: Testing Feature Preparation")
    logger.info("="*50)
    
    try:
        from feature_engineering import prepare_features
        
        results, team_stats, league_stats, h2h_stats, league_table = data
        
        # Get column mappings
        mappings = {
            "team_stats": {
                "points_per_game": "points_per_game",
                "goals_scored_per_match_home": "goals_scored_per_match_home",
                "goals_scored_per_match_away": "goals_scored_per_match_away",
                "goals_conceded_per_match_home": "goals_conceded_per_match_home",
                "goals_conceded_per_match_away": "goals_conceded_per_match_away",
                "total_home_wins": "total_home_wins",
                "total_home_played": "total_home_played",
                "total_away_wins": "total_away_wins",
                "total_away_played": "total_away_played",
                "total_home_draws": "total_home_draws",
                "total_away_draws": "total_away_draws",
                "ppg_last_8": "ppg_last_8",
                "avg_goals_scored_last_8": "avg_goals_scored_last_8",
                "avg_goals_conceded_last_8": "avg_goals_conceded_last_8",
            },
            "h2h_stats": {
                "team_a_win_percentage": "team_a_win_percentage",
                "team_b_win_percentage": "team_b_win_percentage",
                "draw_percentage": "draw_percentage",
                "total_matches": "total_matches",
                "team_a_goals": "team_a_goals",
                "team_b_goals": "team_b_goals",
                "btts_percentage": "btts_percentage",
            }
        }
        combined_mapping = {**mappings["team_stats"], **mappings["h2h_stats"]}
        
        # Prepare team mappings
        if "TEAM_NAME_MAPPING" not in league_config:
            league_config["TEAM_NAME_MAPPING"] = {}
        
        # Update mapping with any new teams
        for team in team_stats["Team"].unique():
            if team not in league_config["TEAM_NAME_MAPPING"]:
                league_config["TEAM_NAME_MAPPING"][team] = team
        
        logger.info("Preparing features...")
        prepared_data = prepare_features(
            results, team_stats, league_stats, h2h_stats, league_table,
            combined_mapping, league_config["TEAM_NAME_MAPPING"]
        )
        
        if prepared_data is None:
            logger.error("❌ Feature preparation failed")
            return None, None, None
            
        logger.info("✅ Features prepared successfully:")
        logger.info(f"   - Shape: {prepared_data.shape}")
        logger.info(f"   - Columns: {len(prepared_data.columns)}")
        logger.debug(f"   - Column names: {list(prepared_data.columns)}")
        
        # Check for missing values
        missing_values = prepared_data.isnull().sum()
        if missing_values.any():
            logger.warning("⚠️  Missing values found:")
            for col, count in missing_values[missing_values > 0].items():
                logger.warning(f"   {col}: {count} missing values")
        else:
            logger.info("✅ No missing values found")
            
        # Prepare X and y data
        columns_to_drop = [
            "result", "three_way",
            "over_under_1_5", "over_under_2_5", "over_under_3_5",
            "btts", "home_goals", "away_goals", "total_goals",
            "three_way_encoded",
            "over_under_1_5_encoded", "over_under_2_5_encoded",
            "over_under_3_5_encoded", "btts_encoded",
            "form_data_valid_str",
        ]
        
        X = prepared_data.drop(
            [col for col in columns_to_drop if col in prepared_data.columns],
            axis=1
        )
        
        y_dict = {
            "three_way": prepared_data["three_way"],
            "over_under_1_5": prepared_data["over_under_1_5"],
            "over_under_2_5": prepared_data["over_under_2_5"],
            "over_under_3_5": prepared_data["over_under_3_5"],
            "btts": prepared_data["btts"],
        }
        
        label_encoders = prepared_data.attrs.get("label_encoders", {})
        
        logger.info(f"✅ X data shape: {X.shape}")
        logger.info(f"✅ Y data targets: {list(y_dict.keys())}")
        logger.info(f"✅ Label encoders: {list(label_encoders.keys())}")
        
        return X, y_dict, label_encoders
        
    except Exception as e:
        logger.error(f"❌ Error in feature preparation: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None, None, None

def test_model_training(logger, X, y_dict, label_encoders):
    """Test model training functionality."""
    logger.info("\n" + "="*50)
    logger.info("🤖 STEP 3: Testing Model Training")
    logger.info("="*50)

    try:
        from model_training import train_model

        # Validate data before training
        nan_columns = X.columns[X.isna().any()].tolist()
        if nan_columns:
            logger.error("❌ NaN values found in the following columns:")
            for col in nan_columns:
                logger.error(f"   {col}: {X[col].isna().sum()} NaN values")
            return None

        logger.info("Training models...")
        models = train_model(X, y_dict, label_encoders)

        if not models:
            logger.error("❌ No models trained")
            return None

        logger.info("✅ Models trained successfully:")
        for pred_type, model_info in models.items():
            logger.info(f"   - {pred_type}: {type(model_info['model']).__name__}")
            logger.info(f"     Features: {len(model_info['feature_names'])}")
            logger.debug(f"     Feature names: {model_info['feature_names'][:10]}...")

        return models

    except Exception as e:
        logger.error(f"❌ Error in model training: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None

def test_single_match_prediction(logger, models, data, league_config, label_encoders):
    """Test prediction on a single match."""
    logger.info("\n" + "="*50)
    logger.info("⚽ STEP 4: Testing Single Match Prediction")
    logger.info("="*50)

    try:
        from prediction import predict_match

        results, team_stats, league_stats, h2h_stats, league_table = data

        # Get column mappings
        mappings = {
            "team_stats": {
                "points_per_game": "points_per_game",
                "goals_scored_per_match_home": "goals_scored_per_match_home",
                "goals_scored_per_match_away": "goals_scored_per_match_away",
                "goals_conceded_per_match_home": "goals_conceded_per_match_home",
                "goals_conceded_per_match_away": "goals_conceded_per_match_away",
                "total_home_wins": "total_home_wins",
                "total_home_played": "total_home_played",
                "total_away_wins": "total_away_wins",
                "total_away_played": "total_away_played",
                "total_home_draws": "total_home_draws",
                "total_away_draws": "total_away_draws",
                "ppg_last_8": "ppg_last_8",
                "avg_goals_scored_last_8": "avg_goals_scored_last_8",
                "avg_goals_conceded_last_8": "avg_goals_conceded_last_8",
            },
            "h2h_stats": {
                "team_a_win_percentage": "team_a_win_percentage",
                "team_b_win_percentage": "team_b_win_percentage",
                "draw_percentage": "draw_percentage",
                "total_matches": "total_matches",
                "team_a_goals": "team_a_goals",
                "team_b_goals": "team_b_goals",
                "btts_percentage": "btts_percentage",
            }
        }
        combined_mapping = {**mappings["team_stats"], **mappings["h2h_stats"]}

        # Get average goals per match
        avg_goals_per_match = float(
            league_stats[league_stats["Stat"] == "avg_goals_per_match"]["Value"].values[0]
        )

        # Select a test match - let's use Liverpool vs Southampton (strong vs weak team)
        available_teams = team_stats["Team"].unique()
        logger.debug(f"Available teams: {list(available_teams)}")

        # Find suitable teams for testing
        home_team = None
        away_team = None

        # Try to find Liverpool and Southampton
        for team in available_teams:
            if "Liverpool" in team:
                home_team = team
            elif "Southampton" in team:
                away_team = team

        # If not found, try other strong vs weak combinations
        if home_team is None or away_team is None:
            # Look for other strong teams
            strong_teams = []
            weak_teams = []

            for team in available_teams:
                if any(strong in team for strong in ["Liverpool", "Arsenal", "Manchester City", "Chelsea"]):
                    strong_teams.append(team)
                elif any(weak in team for weak in ["Southampton", "Sheffield", "Burnley", "Norwich", "Watford"]):
                    weak_teams.append(team)

            if strong_teams and weak_teams:
                home_team = strong_teams[0]
                away_team = weak_teams[0]
            else:
                # Fallback to first two teams
                home_team = available_teams[0]
                away_team = available_teams[-1]  # Use last team to get different teams

        logger.info(f"Testing prediction for: {home_team} vs {away_team}")

        # Log relevant team stats for the prediction
        home_stats = team_stats[team_stats["Team"] == home_team].iloc[0]
        away_stats = team_stats[team_stats["Team"] == away_team].iloc[0]

        logger.info(f"📊 {home_team} stats: PPG={home_stats['points_per_game']:.2f}, Goals/Game={home_stats['goals_scored_per_match_all']:.2f}")
        logger.info(f"📊 {away_team} stats: PPG={away_stats['points_per_game']:.2f}, Goals/Game={away_stats['goals_scored_per_match_all']:.2f}")

        # Make prediction
        pred_results, error_message, correct_scores = predict_match(
            models,
            home_team,
            away_team,
            team_stats,
            league_stats,
            h2h_stats,
            league_table,
            combined_mapping,
            models["three_way"]["feature_names"],
            avg_goals_per_match,
            label_encoders=label_encoders,
            bias_correction=0.05,
            log_features=True
        )

        if error_message:
            logger.error(f"❌ Prediction error: {error_message}")
            return None

        logger.info("✅ Prediction completed successfully!")

        # Log prediction results
        logger.info("📊 PREDICTION RESULTS:")
        logger.info(f"Match: {home_team} vs {away_team}")

        if "main_predictions" in pred_results:
            for pred_type, prediction in pred_results["main_predictions"].items():
                logger.info(f"\n{pred_type.upper()}:")
                if isinstance(prediction, dict):
                    if "prediction" in prediction:
                        logger.info(f"   Prediction: {prediction['prediction']}")
                    if "probabilities" in prediction:
                        logger.info("   Probabilities:")
                        for outcome, prob in prediction["probabilities"].items():
                            logger.info(f"     {outcome}: {prob:.3f}")

        if "expected_goals" in pred_results:
            xg = pred_results["expected_goals"]
            logger.info(f"\nExpected Goals:")
            logger.info(f"   Home: {xg['home']:.2f}")
            logger.info(f"   Away: {xg['away']:.2f}")

        if correct_scores:
            logger.info(f"\nTop Correct Scores:")
            sorted_scores = sorted(correct_scores.items(), key=lambda x: x[1], reverse=True)
            for score, prob in sorted_scores[:5]:
                logger.info(f"   {score}: {prob:.3f}")

        return pred_results, correct_scores

    except Exception as e:
        logger.error(f"❌ Error in match prediction: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None

def test_analysis_and_output(logger, pred_results, correct_scores):
    """Test analysis and output generation."""
    logger.info("\n" + "="*50)
    logger.info("📈 STEP 5: Testing Analysis and Output")
    logger.info("="*50)

    try:
        from prediction import (
            analyze_prediction_confidence,
            assess_prediction_risk,
            generate_prediction_summary,
            save_predictions_to_excel
        )

        if pred_results is None:
            logger.error("❌ No prediction results to analyze")
            return False

        predictions = pred_results.get("main_predictions", {})

        # Analyze confidence
        logger.info("Analyzing prediction confidence...")
        confidence_analysis = analyze_prediction_confidence(predictions)
        logger.info("✅ Confidence analysis completed")
        logger.debug(f"Confidence analysis: {json.dumps(confidence_analysis, cls=NumpyEncoder, indent=2)}")

        # Assess risk
        logger.info("Assessing prediction risk...")
        risk_assessment = assess_prediction_risk(predictions, confidence_analysis)
        logger.info("✅ Risk assessment completed")
        logger.debug(f"Risk assessment: {json.dumps(risk_assessment, cls=NumpyEncoder, indent=2)}")

        # Generate summary
        logger.info("Generating prediction summary...")
        match_summary = generate_prediction_summary(
            predictions,
            correct_scores,
            confidence_analysis,
            risk_assessment=risk_assessment
        )
        logger.info("✅ Prediction summary generated")

        # Save to Excel
        logger.info("Saving predictions to Excel...")
        match_predictions = {"Test Match": match_summary}
        save_predictions_to_excel(match_predictions, "ENGLAND_PREMIER_LEAGUE_TEST")
        logger.info("✅ Predictions saved to Excel file")

        return True

    except Exception as e:
        logger.error(f"❌ Error in analysis and output: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Main test function."""
    # Setup logging
    logger, log_filename = setup_detailed_logging()

    try:
        # Step 1: Test data loading
        data, league_config = test_data_loading(logger)
        if data is None:
            logger.error("❌ Data loading failed. Exiting.")
            return

        # Step 2: Test feature preparation
        X, y_dict, label_encoders = test_feature_preparation(logger, data, league_config)
        if X is None:
            logger.error("❌ Feature preparation failed. Exiting.")
            return

        # Step 3: Test model training
        models = test_model_training(logger, X, y_dict, label_encoders)
        if models is None:
            logger.error("❌ Model training failed. Exiting.")
            return

        # Step 4: Test single match prediction
        prediction_result = test_single_match_prediction(logger, models, data, league_config, label_encoders)
        if prediction_result is None:
            logger.error("❌ Match prediction failed. Exiting.")
            return

        pred_results, correct_scores = prediction_result

        # Step 5: Test analysis and output
        analysis_success = test_analysis_and_output(logger, pred_results, correct_scores)
        if not analysis_success:
            logger.error("❌ Analysis and output failed.")
            return

        # Final summary
        logger.info("\n" + "="*80)
        logger.info("🎉 PREDICTION PIPELINE TEST COMPLETED SUCCESSFULLY!")
        logger.info("="*80)
        logger.info("✅ All pipeline components tested successfully:")
        logger.info("   ✓ Data loading")
        logger.info("   ✓ Feature preparation")
        logger.info("   ✓ Model training")
        logger.info("   ✓ Match prediction")
        logger.info("   ✓ Analysis and output")
        logger.info(f"\n📄 Detailed logs saved to: {log_filename}")
        logger.info(f"📊 Excel output saved to: data/processed/")
        logger.info(f"🕒 Test completed at: {datetime.now()}")

    except Exception as e:
        logger.error(f"❌ Unexpected error in main: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
