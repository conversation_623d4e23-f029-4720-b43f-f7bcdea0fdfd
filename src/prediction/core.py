"""
Core prediction module containing the main match prediction logic.
"""

import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from typing import Dict, <PERSON><PERSON>, Optional, Any, Union
from scipy.stats import poisson

from feature_engineering import engineer_features
from feature_engineering.h2h import calculate_h2h_strength
from .constants import (
    MODEL_WEIGHT,
    XG_WEIGHT,
    MIN_PROBABILITY,
    MAX_PROBABILITY,
    PREDICTION_TYPES,
    MIN_MATCHES_FOR_VALID_FORM,
    STRENGTH_ADJUSTMENT_FACTOR,
    FORM_ADJUSTMENT_FACTOR,
    STRONG_HOME_BOOST,
    NORMAL_HOME_BOOST,
    NORMAL_AWAY_REDUCTION,
    BASE_AWAY_ADJUSTMENT,
    AWAY_STRENGTH_FACTOR
)
from .expected_goals import calculate_expected_goals
from .probabilities import blend_predictions, calibrate_probabilities
from .scores import predict_correct_scores
from .validation import validate_predictions
from .analysis import analyze_prediction_confidence, assess_prediction_risk

logger = logging.getLogger(__name__)

def format_prediction(name: str, probabilities: Dict[str, float]) -> Dict[str, Union[str, Dict[str, float]]]:
    """
    Format prediction output based on market type.

    Args:
        name: Name of the market (e.g., 'three_way', 'over_under_2_5')
        probabilities: Dictionary of outcome probabilities

    Returns:
        Dictionary containing prediction and probabilities
    """
    max_prob = max(probabilities.values())
    prediction = [k for k, v in probabilities.items() if v == max_prob][0]

    if name.startswith('over_under'):
        # Extract threshold from market name (e.g. "over_under_2_5" -> "2.5")
        threshold = name.split('_')[-2] + '.' + name.split('_')[-1]
        
        formatted_probs = {
            f'Under {threshold}': probabilities['Under'],
            f'Over {threshold}': probabilities['Over']
        }
        
        formatted_prediction = f'{prediction} {threshold}'
        
        return {
            'prediction': formatted_prediction,
            'probabilities': formatted_probs
        }
    else:
        return {
            'prediction': prediction,
            'probabilities': probabilities
        }

def get_threshold_from_pred_type(pred_type: str) -> float:
    """
    Extract threshold value from prediction type string.

    Args:
        pred_type: Type of prediction (e.g., 'over_under_2_5')

    Returns:
        Threshold value as float
    """
    try:
        parts = pred_type.split("_")
        return float(f"{parts[-2]}.{parts[-1]}")
    except (IndexError, ValueError) as e:
        logger.error(f"Error extracting threshold from {pred_type}: {str(e)}")
        return 0.0

def predict_match(
    models: Dict[str, Any],
    home_team: str,
    away_team: str,
    team_stats: pd.DataFrame,
    league_stats: pd.DataFrame,
    h2h_stats: pd.DataFrame,
    league_table: pd.DataFrame,
    column_mapping: Dict[str, str],
    feature_names: list,
    avg_goals_per_match: float,
    label_encoders: Optional[Dict[str, Any]] = None,
    bias_correction: float = 0.05,
    log_features: bool = True,
) -> Tuple[Dict[str, Any], Optional[str], Dict[str, float]]:
    """
    Predict the outcome of a match with enhanced prediction logic and post-processing.
    """
    logger.debug(f"Starting predict_match for {home_team} vs {away_team}")

    try:
        # Validate input data
        validation_result = _validate_input_data(
            home_team, away_team,
            team_stats, league_stats
        )
        if validation_result:
            return {}, validation_result, {}

        # Get team and league statistics
        stats_data = _get_statistics_data(
            home_team, away_team,
            team_stats, league_stats, h2h_stats
        )
        if not stats_data:
            return {}, "Error getting statistics data", {}

        home_stats, away_stats, league_stats_dict, h2h_data = stats_data

        # Calculate strengths and expected goals
        strengths_data = _calculate_strengths_and_goals(
            home_stats, away_stats,
            league_stats_dict, h2h_data,
            home_team, away_team
        )
        if not strengths_data:
            return {}, "Error calculating strengths and goals", {}

        h2h_strength, home_xg, away_xg = strengths_data

        # Engineer features
        features_data = _engineer_match_features(
            home_team, away_team,
            home_stats, away_stats,
            h2h_data, league_stats_dict,
            league_table, column_mapping,
            label_encoders, home_xg, away_xg,
            log_features
        )
        if not features_data:
            return {}, "Error engineering features", {}

        new_match, new_match_df = features_data

        # Make predictions
        predictions = _make_predictions(
            models, new_match_df,
            home_xg, away_xg,
            new_match, feature_names
        )

        # Calculate correct scores
        correct_scores = predict_correct_scores(
            (home_xg, away_xg),
            team_strengths=None,
            h2h_strength=h2h_strength
        )

        # Add additional information
        predictions["expected_goals"] = {"home": home_xg, "away": away_xg}
        predictions["correct_scores"] = correct_scores
        predictions["form_data_valid"] = new_match.get("form_data_valid", 0)
        predictions["form_data_valid_str"] = (
            "Valid" if predictions["form_data_valid"] == 1 else "Invalid"
        )

        return predictions, None, correct_scores

    except Exception as e:
        logger.error(f"Error in predict_match: {str(e)}")
        return {"main_predictions": {}, "expected_goals": {"home": 0, "away": 0}}, f"Error in predict_match: {str(e)}", {}

def _validate_input_data(
    home_team: str,
    away_team: str,
    team_stats: pd.DataFrame,
    league_stats: pd.DataFrame
) -> Optional[str]:
    """Validate input data for prediction."""
    if league_stats.empty:
        return "Error: league_stats DataFrame is empty"

    if home_team not in team_stats["Team"].values:
        return f"Error: Home team {home_team} not found in team_stats"

    if away_team not in team_stats["Team"].values:
        return f"Error: Away team {away_team} not found in team_stats"

    return None

def _get_statistics_data(
    home_team: str,
    away_team: str,
    team_stats: pd.DataFrame,
    league_stats: pd.DataFrame,
    h2h_stats: pd.DataFrame
) -> Optional[Tuple[pd.Series, pd.Series, Dict[str, float], Optional[pd.Series]]]:
    """Get team, league, and h2h statistics."""
    try:
        home_stats = team_stats[team_stats["Team"] == home_team].iloc[0]
        away_stats = team_stats[team_stats["Team"] == away_team].iloc[0]
        league_stats_dict = dict(zip(league_stats["Stat"], league_stats["Value"]))

        h2h_row = h2h_stats[
            (h2h_stats["Matchup"] == f"{home_team} vs {away_team}") |
            (h2h_stats["Matchup"] == f"{away_team} vs {home_team}")
        ]
        h2h_data = None if h2h_row.empty else h2h_row.iloc[0]

        return home_stats, away_stats, league_stats_dict, h2h_data

    except Exception as e:
        logger.error(f"Error getting statistics data: {str(e)}")
        return None

def _calculate_strengths_and_goals(
    home_stats: pd.Series,
    away_stats: pd.Series,
    league_stats_dict: Dict[str, float],
    h2h_data: Optional[pd.Series],
    home_team: str,
    away_team: str
) -> Optional[Tuple[Optional[Dict[str, float]], float, float]]:
    """Calculate team strengths and expected goals."""
    try:
        h2h_strength = None
        if h2h_data is not None:
            h2h_strength = calculate_h2h_strength(h2h_data, home_team, away_team)

        home_xg, away_xg = calculate_expected_goals(
            home_stats, away_stats, league_stats_dict
        )
        logger.info(f"Expected goals - Home: {home_xg:.2f}, Away: {away_xg:.2f}")

        return h2h_strength, home_xg, away_xg

    except Exception as e:
        logger.error(f"Error calculating strengths and goals: {str(e)}")
        return None

def _engineer_match_features(
    home_team: str,
    away_team: str,
    home_stats: pd.Series,
    away_stats: pd.Series,
    h2h_data: Optional[pd.Series],
    league_stats_dict: Dict[str, float],
    league_table: pd.DataFrame,
    column_mapping: Dict[str, str],
    label_encoders: Optional[Dict[str, Any]],
    home_xg: float,
    away_xg: float,
    log_features: bool
) -> Optional[Tuple[Dict[str, Any], pd.DataFrame]]:
    """Engineer features for match prediction."""
    try:
        new_match = engineer_features(
            home_team, away_team,
            home_stats, away_stats,
            h2h_data, league_stats_dict,
            league_table, column_mapping,
            {}, label_encoders
        )

        if new_match is None:
            return None

        # Add expected goals to features
        new_match["expected_goals_home"] = home_xg
        new_match["expected_goals_away"] = away_xg
        new_match["expected_goals_total"] = home_xg + away_xg

        # Add form data validity if not present
        if "form_data_valid" not in new_match:
            home_matches = home_stats["total_home_played"] + home_stats["total_away_played"]
            away_matches = away_stats["total_home_played"] + away_stats["total_away_played"]
            new_match["form_data_valid"] = int(
                min(home_matches, away_matches) >= MIN_MATCHES_FOR_VALID_FORM
            )

        if log_features:
            logger.info(f"\nFeature values for {home_team} vs {away_team}:")
            for feature, value in new_match.items():
                logger.info(f"{feature}: {value}")

        new_match_df = pd.DataFrame([new_match])

        return new_match, new_match_df

    except Exception as e:
        logger.error(f"Error engineering match features: {str(e)}")
        return None

def _make_predictions(
    models: Dict[str, Any],
    new_match_df: pd.DataFrame,
    home_xg: float,
    away_xg: float,
    features: dict,
    feature_names: list
) -> Dict[str, Any]:
    """Make predictions using all models."""
    predictions = {"main_predictions": {}}

    # Ensure all required features are present
    for feature in feature_names:
        if feature not in new_match_df.columns:
            logger.warning(f"Feature '{feature}' is missing. Setting it to 0.")
            new_match_df[feature] = 0

    new_match_df = new_match_df[feature_names]

    # Make predictions for each model (excluding double_chance which is calculated from three_way)
    for pred_type, model_info in models.items():
        try:
            model = model_info["model"]
            scaler = model_info.get("scaler")

            # Scale features if scaler is available (critical for model accuracy)
            scaled_features = new_match_df.copy()
            if scaler is not None:
                logger.debug(f"Scaling features for {pred_type} prediction")
                scaled_features = pd.DataFrame(
                    scaler.transform(new_match_df),
                    columns=new_match_df.columns,
                    index=new_match_df.index
                )
            else:
                logger.warning(f"No scaler found for {pred_type} - using unscaled features")

            if isinstance(model, tf.keras.Sequential):
                probabilities = _make_neural_network_prediction(
                    model, scaled_features, pred_type,
                    home_xg, away_xg, features
                )
            else:
                probabilities = _make_sklearn_prediction(
                    model, scaled_features, pred_type,
                    home_xg, away_xg
                )

            # Format prediction using the format_prediction function
            predictions["main_predictions"][pred_type] = format_prediction(pred_type, probabilities)

        except Exception as e:
            logger.error(f"Error in making prediction for {pred_type}: {str(e)}")
            predictions["main_predictions"][pred_type] = {
                "prediction": "Error",
                "probabilities": {}
            }

    # Calculate double_chance from three_way probabilities
    if "three_way" in predictions["main_predictions"]:
        from prediction.probabilities import calculate_double_chance_probabilities

        three_way_probs = predictions["main_predictions"]["three_way"].get("probabilities", {})
        if three_way_probs:
            double_chance_probs = calculate_double_chance_probabilities(three_way_probs)

            # Find the prediction with highest probability
            best_outcome = max(double_chance_probs.items(), key=lambda x: x[1])

            predictions["main_predictions"]["double_chance"] = {
                "prediction": best_outcome[0],
                "probabilities": double_chance_probs
            }

    return predictions

def _make_neural_network_prediction(
    model: tf.keras.Model,
    new_match_df: pd.DataFrame,
    pred_type: str,
    home_xg: float,
    away_xg: float,
    features: dict
) -> Dict[str, float]:
    """Make prediction using neural network model."""
    raw_probabilities = model.predict(new_match_df, verbose=0)[0]

    if pred_type == "three_way":
        # Apply softmax and create probability dictionary
        exp_probs = np.exp(raw_probabilities - np.max(raw_probabilities))
        probabilities = exp_probs / exp_probs.sum()
        prob_dict = {
            "Home": float(probabilities[0]),
            "Draw": float(probabilities[1]),
            "Away": float(probabilities[2])
        }

        # Adjust probabilities based on team strengths and form
        home_strength = features.get("home_recent_performance", 0)
        away_strength = features.get("away_recent_performance", 0)
        home_form = features.get("home_form", 0)
        away_form = features.get("away_form", 0)

        strength_diff = home_strength - away_strength
        form_diff = home_form - away_form

        # Apply strength and form adjustments
        home_adjustment = STRENGTH_ADJUSTMENT_FACTOR * (strength_diff + form_diff)
        away_adjustment = -home_adjustment

        # Apply adjustments with bounds
        prob_dict["Home"] = min(MAX_PROBABILITY, max(MIN_PROBABILITY,
                                                   prob_dict["Home"] + home_adjustment))
        prob_dict["Away"] = min(MAX_PROBABILITY, max(MIN_PROBABILITY,
                                                   prob_dict["Away"] + away_adjustment))
        prob_dict["Draw"] = max(MIN_PROBABILITY, 1 - prob_dict["Home"] - prob_dict["Away"])

        # Normalize probabilities
        total = sum(prob_dict.values())
        return {k: v / total for k, v in prob_dict.items()}

    else:
        # Handle over/under predictions
        probabilities = raw_probabilities.tolist()
        if pred_type.startswith("over_under"):
            threshold = get_threshold_from_pred_type(pred_type)
            total_xg = home_xg + away_xg

            # Calculate Poisson probability
            poisson_under_prob = sum(
                poisson.pmf(k, total_xg) for k in range(int(threshold))
            )
            poisson_over_prob = 1 - poisson_under_prob

            # Blend probabilities
            under_prob = MODEL_WEIGHT * probabilities[0] + XG_WEIGHT * poisson_under_prob
            over_prob = MODEL_WEIGHT * probabilities[1] + XG_WEIGHT * poisson_over_prob

            # Normalize probabilities
            total = under_prob + over_prob
            return {
                "Under": under_prob / total,
                "Over": over_prob / total
            }
        elif pred_type == "btts":
            return {
                "No": float(probabilities[0]),
                "Yes": float(probabilities[1])
            }

        return {"Error": 1.0}

def _make_sklearn_prediction(
    model: Any,
    new_match_df: pd.DataFrame,
    pred_type: str,
    home_xg: float,
    away_xg: float
) -> Dict[str, float]:
    """Make prediction using sklearn model."""
    try:
        probabilities = model.predict_proba(new_match_df)[0]

        if pred_type.startswith("over_under"):
            threshold = get_threshold_from_pred_type(pred_type)
            total_xg = home_xg + away_xg

            # Calculate Poisson probability
            poisson_under_prob = sum(
                poisson.pmf(k, total_xg) for k in range(int(threshold))
            )
            poisson_over_prob = 1 - poisson_under_prob

            # Blend probabilities
            under_prob = MODEL_WEIGHT * probabilities[0] + XG_WEIGHT * poisson_under_prob
            over_prob = MODEL_WEIGHT * probabilities[1] + XG_WEIGHT * poisson_over_prob

            # Normalize probabilities
            total = under_prob + over_prob
            return {
                "Under": under_prob / total,
                "Over": over_prob / total
            }

        elif pred_type == "btts":
            return {
                "No": float(probabilities[0]),
                "Yes": float(probabilities[1])
            }

        elif pred_type == "three_way":
            return {
                "Home": float(probabilities[0]),
                "Draw": float(probabilities[1]),
                "Away": float(probabilities[2])
            }

        else:
            return dict(zip(model.classes_, probabilities))

    except Exception as e:
        logger.error(f"Error in sklearn prediction for {pred_type}: {str(e)}")
        return {"Error": 1.0}
